/*
 * @Date: 2024-10-14 15:31:15
 * @Author: elisa.z<PERSON>
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-05-07 16:21:03
 * @FilePath: /lala-finance-biz-web/src/components/AsyncExport/types/index.ts
 * @Description:
 */
// REPAYMENT_LOAN_MGR(1, "还款管理(圆易借)"),
// REPAYMENT_FINANCE_MGR(2, "还款管理(融资租赁)"),
// REPAYMENT_INSURANCE_MGR(3, "还款管理(车险分期)"),
// REPAYMENT_RECORD(4, "还款登记"),
// REPAYMENT_INSURANCE_RECORD(5, "还款登记(车险分期)"),
// RECONCILIATION_MGR(6, "调账管理");
// REPAY_BILL(6, “还款账单");
// REPAY_CAR_INSURANCE_BILL(7, "车险分期还款账单");
// REPAY_BILL_APPROVE(8, "账单审核");
// REPAY_LEASE_BILL(17, "融资租赁期账");
// REPAY_LEASE_BILL_TOTAL(18, "融资租赁总账");
// REPAY_APPLY_REMISSION_APPROVE(21, "还款单减免")

// code 是数字 En 英文
export enum ItaskCodeEnValueEnum {
  REPAYMENT_LOAN_MGR = 1,
  REPAYMENT_FINANCE_MGR = 2,
  REPAYMENT_INSURANCE_MGR = 3,
  REPAYMENT_RECORD = 4,
  REPAYMENT_INSURANCE_RECORD = 5,
  RECONCILIATION_MGR = 6,
  REPAY_BILL = 7,
  REPAY_CAR_INSURANCE_BILL = 8,
  REPAY_BILL_APPROVE = 9,
  ORDER_LEASE = 10,
  SEND_MAIL_RECORD = 11,
  REPAYMENT_DETAIL_LEASE = 12,
  REPAYMENT_DETAIL_INSURANCE = 13,
  REPAY_CAR_INSURANCE_BILL_TOTAL = 14,
  INSURANCE_CANCEL_EXPORT = 15, // 退保结项
  ERROR_DATA_LEASE = 16,
  REPAY_LEASE_BILL = 17,
  REPAY_LEASE_BILL_TOTAL = 18,
  INSURANCE_POLICY_ORDER_MGR = 19,
  LENDING_MGR = 20,
  REPAY_APPLY_REMISSION_APPROVE = 21, //  还款单减免
  INSURANCE_POLICY_MGR = 22, // 车险保单管理
  INSURANCE_POLICY_MGR_EXPORT = 23,
  ENTERPRISE_AUTH_EXPORT = 24, //企业认证管理列表导出
  CAR_MODEL_MGR = 25, // 融租车型管理
  NOTARIZATION_LIST = 26, // 导出赋强公证清单
  PERSONAL_ACTIVATE_MANAGER = 29, // 个人进件管理列表导出
  PERSONAL_USER_MANAGER = 30, // 个人用户管理列表导出
  CASH_ORDER_MANAGER = 31, // 订单管理(小贷)
  CALL_PAYMENT_OVERDUE_REFUND = 32, // 催收回款
  CALL_PAYMENT_OFFLINE_REFUND_SINGLE_PERIOD = 33, // 单期还款
  CALL_PAYMENT_OFFLINE_REFUND_COMPENSATORY = 34, // 代偿结清
  CALL_PAYMENT_EARLY_SETTLEMENT = 35, // 提前结清
  INSURANCE_ADD_SUBTRACT = 36, // 加减保
  ACCOUNT_CHECK_BILL_REPAY_DETAIL_EXPORT = 37, // 查账-入帐明细
  ACCOUNT_CHECK_BILL_REPAY_FLOW_EXPORT = 38, // 查账-流水
}

export const taskCodeValueZnMap = {
  1: '还款管理(圆易借)',
  2: '还款管理(融资租赁)',
  3: '还款管理(车险分期)',
  4: '还款登记',
  5: '还款登记(车险分期)',
  6: '调账管理',
  7: '还款账单',
  8: '车险分期还款账单',
  9: '账单审核',
  10: '融租订单管理',
  11: '邮件发送记录',
  12: '还款明细导出(融租&其他)',
  13: '还款明细导出(车险分期)',
  14: '车险分期车辆总账',
  15: '退保管理',
  16: '融租差错数据',
  17: '融资租赁期账',
  18: '融资租赁总账',
  19: '订单管理(车险分期)',
  20: '放款管理',
  21: '还款单减免',
  22: '车险保单管理列表导出',
  23: '车险保单导出',
  24: '企业认证管理列表导出',
  25: '融租车型管理',
  26: '导出赋强公证清单',
  29: '个人进件管理列表导出',
  30: '个人用户管理列表导出',
  31: '订单管理(小贷)',
  32: '催收回款',
  33: '单期还款',
  34: '代偿结清',
  35: '提前结清',
  36: '车险分期加/减保',
  37: '查账对账-入账明细',
  38: '查账对账-流水',
};

export type ItaskCode = keyof typeof taskCodeValueZnMap;

// QUEUE(1, "队列中"),
// EXECUTING(2, "执行中"),
// SUCCESS(3, "导出完成"),
// FAIL(4, "导出失败");
// PARTIAL_SUCCESS(5, "导出部分成功");

export enum IexportStatusEnValueEnum {
  QUEUE = 1,
  EXECUTING = 2,
  SUCCESS = 3,
  FAIL = 4,
  PARTIAL_SUCCESS = 5,
}

export const canDownloadStatus = [
  IexportStatusEnValueEnum.SUCCESS,
  IexportStatusEnValueEnum.PARTIAL_SUCCESS,
];

export const exportStatusValueZnMap = {
  1: '队列中',
  2: '执行中',
  3: '导出完成',
  4: '导出失败',
  5: '导出部分成功',
};

export type IexportStatus = keyof typeof exportStatusValueZnMap;

export interface IexportRecord {
  createdAt: string; // 创建时间
  executeDatetime: string; // 开始处理时间
  exporterName: string; // 导出人姓名
  exporterPid: number; // 导出人pid
  finishDatetime: string; // 完成时间
  ossPath: string; // 文件地址
  status: IexportStatus; // 状态
  submitDatetime: string; // 提交时间
  taskCode: number; // 任务编码
  totalCount: number; // 总条数
  updatedAt: string; // 更新时间
}
