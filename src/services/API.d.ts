/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2021-11-05 18:10:28
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2024-02-21 16:35:09
 * @FilePath: /lala-finance-biz-web/src/services/API.d.ts
 * @Description: API
 */
declare namespace API {
  export interface CurrentUser {
    accountName: string;
    token: string;
    access: any[];
    userId: string;
    userName: string;
    role: any[];
    pid?: string; //通行证id
    channelCode?: string; //  渠道编码
    channelType?: string; //  渠道类型
    extSource?: {
      //  其他信息（门店信息等）
      storeId?: string;
      storeName?: string;
    };
    employeeType?: number; // 9运营| 10车险渠道| 11委外
    devAccessWhiteList?: any[]; // 开发者配置权限
    passportName?: string; // 飞书通行证姓名
    // avatar?: string;
    // name?: string;
    // title?: string;
    // group?: string;
    // signature?: string;
    // tags?: {
    //   key: string;
    //   label: string;
    // }[];
    // userid?: string;
    // access?: 'user' | 'guest' | 'admin';
    // unreadCount?: number;
    isGrayUser?: boolean; // 2025-05-13 新增，后续需移除
  }

  // export interface LoginStateType {
  //   status?: 'ok' | 'error';
  //   type?: string;
  // }

  export interface NoticeIconData {
    id: string;
    key: string;
    avatar: string;
    title: string;
    datetime: string;
    type: string;
    read?: boolean;
    description: string;
    clickClose?: boolean;
    extra: any;
    status: string;
  }

  // 全局枚举

  type EnumItem = { key: string; value: string };
  export interface GlobalEnum {
    auditEnum: EnumItem[]; // 申请单状态
    auditRefuseReason: EnumItem[]; // 人工审核拒绝理由
    checkResultRes: EnumItem[]; // 风控日志
    creditAuditRes: EnumItem[]; // 风控等级
    productTypeRes: EnumItem[]; // 产品类型
  }
}

export interface DownZips {
  billNo?: string;
  contractFileReq?: { contractName?: string; contractType?: number; filePath?: string }[];
  orderNo?: string;
  productCode?: string;
  userName?: string;
}
