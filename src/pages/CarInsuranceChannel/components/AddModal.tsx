import { EcarInsuranceChannelType } from '@/utils/bankend/enum';
import type { ProFormInstance } from '@ant-design/pro-components';
import { ModalForm, ProFormSelect, ProFormText } from '@ant-design/pro-form';
import type { ActionType } from '@ant-design/pro-table';
import { Form, message } from 'antd';
import type { ReactElement } from 'react';
import React, { memo, useRef } from 'react';
import { add } from '../services';
import type { IcarInsuranceChannelItem } from '../type';
import { getPersonalOrEnterpriseHasVal } from '../util';
import CorRelation from './CorRelation';
type Props = {
  children: ReactElement;
  action: ActionType;
  isSecondList: boolean;
  parentChannelInfo: Record<string, any>;
};
const AddModal: React.FC<Props> = (props) => {
  const corRelationRef = useRef<any>();

  const formRef = useRef<ProFormInstance>();
  const { parentChannelInfo, isSecondList } = props;
  return (
    <ModalForm<IcarInsuranceChannelItem>
      title="新增车险分期渠道"
      formRef={formRef}
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 14 }}
      modalProps={{
        destroyOnClose: true,
      }}
      layout="horizontal"
      trigger={props.children}
      onFinish={async (values) => {
        const personalRelatedPayAccount = formRef?.current?.getFieldValue(
          'personalRelatedPayAccount',
        );
        const { personal, enterprise } = getPersonalOrEnterpriseHasVal(values);
        let personalRelatedPayAccountT, relatedPayAccountT;
        //个人配置公用保司选择，后端字段依旧沿用两个。需要根据产品与企业的是否选中判断一下
        if (personal) {
          personalRelatedPayAccountT = personalRelatedPayAccount;
        }
        if (enterprise) {
          relatedPayAccountT = personalRelatedPayAccount;
        }
        const channelLevel = isSecondList ? 2 : 1;
        const parentChannelCode = parentChannelInfo?.channelCode;
        // console.log(personalRelatedPayAccount);
        // return;
        await add({
          ...values,
          personalRelatedPayAccount: personalRelatedPayAccountT,
          relatedPayAccount: relatedPayAccountT,
          channelLevel,
          parentChannelCode,
        });
        message.success('提交成功');
        props?.action.reload();
        return true;
      }}
      onValuesChange={(val, values) => {
        //  校验规则 有个全选下拉框 无法触发onValuesChange 由于setfieldsvalue
        corRelationRef.current?.onCheckRules(values);
      }}
      initialValues={{
        relatedProductAndEnterpriseFlat: [{}],
      }}
    >
      <Form.Item label="渠道层级" required>
        <span>
          {isSecondList ? `二级渠道（上级:${parentChannelInfo?.channelName}）` : '一级渠道'}
        </span>
      </Form.Item>
      <ProFormSelect
        name="channelType"
        label="渠道类型"
        placeholder="请选择渠道类型"
        rules={[{ required: true }]}
        options={Object.keys(EcarInsuranceChannelType).map((item) => {
          return {
            label: EcarInsuranceChannelType[item],
            value: item,
          };
        })}
      />
      <ProFormText
        name="channelShortName"
        label="渠道简称"
        placeholder="请输入渠道简称"
        required
        rules={[{ required: true }]}
      />
      <ProFormText
        name="channelName"
        label="渠道全称"
        placeholder="请输入渠道全称"
        required
        rules={[{ required: true }]}
      />
      <ProFormText
        name="orgCode"
        label="社会统一信用代码"
        placeholder="请输入社会统一信用代码"
        required
        rules={[{ required: true }]}
      />
      <ProFormText
        name="contactName"
        label="联系人"
        placeholder="请输入联系人"
        required
        rules={[{ required: true }]}
      />
      <ProFormText
        name="contactPhone"
        label="联系人电话"
        placeholder="请输入联系人电话"
        required
        rules={[{ required: true }]}
      />
      <ProFormText
        name="contactAddress"
        label="联系人地址"
        placeholder="请输入联系人地址"
        required
        rules={[{ required: true }]}
      />
      <CorRelation
        playground="add"
        corRelationRef={corRelationRef}
        isSecondList={isSecondList}
        parentChannelInfo={parentChannelInfo}
        formRef={formRef}
      />
    </ModalForm>
  );
};
export default memo(AddModal);
