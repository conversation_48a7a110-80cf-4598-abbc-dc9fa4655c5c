import type { IfeeConfigItem, IrepaymentDTO } from '@/pages/Product/type';
import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';
import type { CompensationChannelIF, IcarInsuranceChannelItem } from '../type';

// 添加车险渠道
export async function add(data: IcarInsuranceChannelItem) {
  return request(`/bizadmin/channel`, {
    method: 'POST',
    data,
    headers: bizAdminHeader,
  });
}

export interface IcompanyItem {
  id: string;
  companyName: string; // 公司名称
  paymentAccountNo: string; // 收款账户
  bankName: string; // 账户开户行
  bankNo: string; // 联行号
  subBranchName: string; // 开户支行名称
}
export type IenterpriseItem = {
  id: number;
  certiName: string; // 企业名称
  epAuthNo: string; // 企业认证编号
  certiNo: string; // 证件号码
  authorizerName: string; // 授权签约人
};
export type IproductItem = {
  id: number;
  productName: string;
  productCode: string;
  costConfigs: IfeeConfigItem[]; // 费用配置
  repayment: IrepaymentDTO; // 还款配置
};
export type TgetRelationList = {
  companyList: IcompanyItem[]; // 关联保费收款公司
  enterpriseList: IenterpriseItem[]; // 关联企业
  productList: IproductItem[]; // 关联产品
  personalCompanyList: IcompanyItem[]; // 关联保费收款公司（个人）
  personalProductList: IproductItem[]; // 关联产品（个人）
};
/**
 * 获取关联列表数据
 */
export async function getRelationList(channelCode?: string): Promise<TgetRelationList> {
  const data = await request(`/bizadmin/channel/getCorrelationList`, {
    method: 'GET',
    params: { channelCode },
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
  return data?.data || {};
}

// 修改渠道账户的密码
export async function changePassword(data: {
  userAccount: string;
  newPassword: string;
  pid: string;
}) {
  return request(`/bizadmin/channel/changePassword`, {
    method: 'PUT',
    data,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

// 删除渠道
export async function deleteChannel(id: number) {
  return request(`/bizadmin/channel/${id}`, {
    method: 'DELETE',
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

type IchangeCorrelationParams = {
  id: number; // 渠道id
  channelCode: string;
  relatedProduct: number[]; // 关联的产品id列表
  relatedEnterprise: number[]; // 关联的企业id列表
  relatedPayAccount: number[]; // 关联的收款公司id列表
  personalRelatedPayAccount: string[]; //保司
};
// 修改关联
export async function changeCorrelation(data: IchangeCorrelationParams) {
  return request(`/bizadmin/channel/changeCorrelation`, {
    method: 'PUT',
    data,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}
export interface IgetListParams {
  pageNumber: number;
  pageSize: number;
  channelName: string;
  queryChildren?: boolean;
  channelCode: string;
}

type TgetListRes = {
  data: IcarInsuranceChannelItem[];
  current: number;
  pageSize: number;
  total: number;
};
// 获取列表数据
export async function getList(params: IgetListParams): Promise<TgetListRes> {
  return request(`/bizadmin/channel/list`, {
    method: 'GET',
    params,
    ifTrimParams: true,
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

// 获取角色权限
export async function getRoleList(channelCode: string) {
  return request(`/bizadmin/channel/getChannelRoleCodeList`, {
    params: { channelCode },
    headers: {
      'hll-appid': 'bme-finance-bizadmin-svc',
    },
  });
}

// 获取代偿客户列表
export async function getCompensationCustomerList(data: {
  channelCode?: string;
  productSecondCode?: '0201' | '0303';
  pageSize?: number;
}) {
  return request(`/bizadmin/channel/getERAuthRecordByChannelCode`, {
    method: 'POST',
    data,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 创建代偿渠道发送飞书审批
export async function createCompensationChannel(data: CompensationChannelIF) {
  return request(`/bizadmin/channel/createReimbursement`, {
    method: 'POST',
    data,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 解绑代偿关系
export async function unbindCompensationChannel(params: { authenticationId: string }) {
  return request(`/bizadmin/channel/unbindReimbursement`, {
    method: 'GET',
    params,
    headers: {
      ...bizAdminHeader,
    },
  });
}

// 查询已授权的个人/企业客户
export async function getAuthEffectiveAuthRecordUser(data: {
  entityType: string | number;
  nameLike?: string | undefined;
  pageSize?: number;
  current?: number;
}) {
  return request(`/bizadmin/insurance/policy/account/query/effectiveAuthRecordUser`, {
    method: 'POST',
    data,
    headers: {
      ...bizAdminHeader,
    },
  });
}
