/*
 * @Author: oak.yang <EMAIL>
 * @Date: 2024-01-05 09:46:07
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-08-20 16:35:13
 * @FilePath: /lala-finance-biz-web/src/pages/CarInsuranceChannel/type/index.ts
 * @Description: CarInsuranceChannel/type
/**
 * 列表页
 */
export interface IcarInsuranceChannelItem {
  id: 2;
  channelCode: string; // 渠道编码
  channelType: string; // 渠道类型
  channelShortName: string; // 渠道简称
  channelName: string; // 渠道全称
  orgCode: string; // 统一信用代码
  contactName: string; // 联系人姓名
  contactPhone: string; // 联系人电话
  contactAddress: string; // 联系人地址
  channelAccountNo: number; // 渠道账号ID
  channelUserAccount: number; // 渠道账号，展示用
  relatedProduct: number[]; // 关联的产品列表
  relatedEnterprise: number[]; // 关联的企业列表
  relatedPayAccount: number[]; // 关联的收款公司列表
  personalRelatedProduct: number[]; // 关联的产品列表（个人）
  personalRelatedPayAccount: number[]; // 关联的收款公司列表（个人）
  companyList: { id: number; companyName: string }[];
  enterpriseList: { id: number; certiName: string }[];
  productList: { id: number; productName: string; productCode: string }[];
  personalCompanyList: { id: number; companyName: string }[];
  personalProductList: { id: number; productName: string; productCode: string }[];
  updatedAt: string;
  createdAt: string;
  channelLevel: number;
  parentChannelCode?: string;
  parentChannel?: { id: string; channelCode: string; channelName: string };
  relatedProductAndEnterpriseFlat: { enterprise: string[]; product: string[] }[];
  extMappingAccountBO?: {
    accountId: string; // 内部账号id
    externalAccountId: string; // 外部账号id
    bankNo: string; // 银行卡号
    bankName: string; // 银行卡名
    accountName: string; // 账户名
  }; // 专属账号信息
  withholdingChannels: string;
}

export interface CompensationChannelIF {
  channelCode: string;
  entityTee: string;
  entityTeeName: string;
  entityTeeIdNo: string;
  productSecondCode: string;
  relativePath: string;
}
