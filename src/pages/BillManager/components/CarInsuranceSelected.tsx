import { useModel } from '@umijs/max';
import { Button } from 'antd';
import React, { memo, useMemo } from 'react';
import './index.less';

const CarInsuranceSelected: React.FC<any> = () => {
  const { selectedData, dispatch } = useModel('AccountCheckMng.shared');

  console.log('selectedRows', selectedData.selectedRows);
  console.log('selectedRowKeys', selectedData.selectedRowKeys);

  const flowNoString = useMemo(() => {
    return (
      <>
        已选择 {selectedData.selectedRows.length} 条流水，
        {selectedData.selectedRows.map((item) => item.flowNo).join('、')}
      </>
    );
  }, [selectedData.selectedRows]);

  const handleCancel = () => {
    dispatch({
      type: 'reset-account-check-mng',
    });
  };

  if (!selectedData.selectedRows.length) {
    return null;
  }

  return (
    <div className="car-insurance-selected">
      <div className="car-insurance-selected-flow-no">
        <span>{flowNoString}</span>
        <span>
          {' '}
          合计可用金额：
          {selectedData.selectedAmount} 元
        </span>
      </div>
      <Button
        variant="text"
        color="danger"
        className="car-insurance-selected-cancel"
        onClick={handleCancel}
      >
        取消选择
      </Button>
    </div>
  );
};

export default memo(CarInsuranceSelected);
