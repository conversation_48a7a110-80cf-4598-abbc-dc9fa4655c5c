import HeaderTab from '@/components/HeaderTab/index';
import { isCarInsuranceStoreUser, isChannelStoreUser } from '@/utils/utils'; // @ts-ignore
import { ProFormSelect } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { useAccess } from '@umijs/max';
// import { Tabs } from 'antd';
import React, { useEffect, useState } from 'react';
import { KeepAlive } from 'react-activation';
import CarPaymentTermList from '../AfterLoan/carInsurance/CarPaymentTermList'; //  车险先做，代码模块保留原来的位置
import CarTotalBillList from '../AfterLoan/carInsurance/CarTotalBillList'; //  车险先做，代码模块保留原来的位置
import OrderPaymentTermList from '../AfterLoan/carInsurance/OrderPaymentTermList'; //  车险先做，代码模块保留原来的位置
import LoanPeriodAccount from '../BusinessCashMng/components/LoanPeriodAccount'; // 圆易借 - 订单期账
import LoanTotalAccount from '../BusinessCashMng/components/LoanTotalAccount'; // 圆易借 - 账单总账
import CarInsuranceSelected from './components/CarInsuranceSelected';
import FinancePaymentTermList from './financeLease/FinancePaymentTermList';
import FinanceTotalBillList from './financeLease/FinanceTotalBillList';
import type {
  CLASSIFICATION_BILL_TYPE,
  CLASSIFICATION_ORDER_BILL_TYPE,
} from './financeLease/types';
import { classificationOrderBill, CLASSIFICATION_BILL } from './financeLease/types';

// const { TabPane } = Tabs;

const BillManagerList: React.FC<any> = () => {
  const [classification, setClassification] = useState<CLASSIFICATION_BILL_TYPE>(''); // 二级分类
  const [classificationOrder, setClassificationOrder] = useState<CLASSIFICATION_ORDER_BILL_TYPE>(
    'ORDER_BILL',
  ); // 账单纬度分类，默认展示【车险分期-车辆总账】
  // const [activeKey, setActiveKey] = useState(`${classification}_${classificationOrder}`);

  const access = useAccess();
  // 登录后默认值初始化
  useEffect(() => {
    if (isCarInsuranceStoreUser(access)) {
      setClassification('CAR_INSURANCE');
      // setActiveKey('CAR_INSURANCE_ORDER_BILL');
    } else if (isChannelStoreUser(access)) {
      setClassification('FINANCE_LEASE');
      // setActiveKey('FINANCE_LEASE_ORDER_BILL');
    } else {
      setClassification('CAR_INSURANCE'); //  默认显示车险分期-车辆总账
    }
  }, [access]);

  function getExtra() {
    return (
      <div style={{ display: 'flex', gap: 20, alignItems: 'center', flex: 1 }}>
        <ProFormSelect
          formItemProps={{
            style: { margin: 0 },
          }}
          fieldProps={{
            value: classification,
            onChange: (val) => {
              setClassification(val);
              setClassificationOrder('ORDER_BILL');
              // setActiveKey(`${val}_ORDER_BILL`);
            },
            allowClear: false,
            disabled: isChannelStoreUser(access) || isCarInsuranceStoreUser(access),
          }}
          label="产品二级分类"
          valueEnum={CLASSIFICATION_BILL}
        />
        <ProFormSelect
          formItemProps={{
            style: { margin: 0 },
          }}
          fieldProps={{
            value: classificationOrder,
            onChange: (val) => {
              setClassificationOrder(val);
              // setActiveKey(`${classification}_${val}`);
            },
            allowClear: false,
          }}
          label="账单纬度"
          valueEnum={classificationOrderBill[classification]}
        />
      </div>
    );
  }
  console.log('二级分类：', classification, '订单纬度：', classificationOrder);
  return (
    <>
      <PageContainer extra={getExtra()}>
        {classification === 'CAR_INSURANCE' && classificationOrder === 'ORDER_BILL' && (
          <>
            {/* 车险-车辆总账 */}
            <CarInsuranceSelected />
            <CarTotalBillList />
          </>
        )}
        {classification === 'CAR_INSURANCE' && classificationOrder === 'ORDER_TERM_BILL' && (
          <>
            {/* 车险-订单期账 */}
            <CarInsuranceSelected />
            <OrderPaymentTermList />
          </>
        )}
        {classification === 'CAR_INSURANCE' && classificationOrder === 'TERM_BILL' && (
          <>
            {/* 车险-车辆期账 */}
            <CarInsuranceSelected />
            <CarPaymentTermList />
          </>
        )}
        {classification === 'FINANCE_LEASE' && classificationOrder === 'ORDER_TERM_BILL' && (
          <FinancePaymentTermList /> //  融租-订单期账
        )}
        {classification === 'FINANCE_LEASE' && classificationOrder === 'ORDER_BILL' && (
          <FinanceTotalBillList /> //  融租-订单总账
        )}
        {
          classification === 'LOAN_INSTALLMENT_CREDIT' &&
            classificationOrder === 'ORDER_TERM_BILL' && <LoanPeriodAccount /> //  圆易借 - 订单期账
        }
        {classification === 'LOAN_INSTALLMENT_CREDIT' && classificationOrder === 'ORDER_BILL' && (
          <LoanTotalAccount /> //  圆易借 - 账单总账
        )}
        {/* <Tabs
          animated={{ inkBar: true, tabPane: true }}
          style={{ background: '#fff', paddingLeft: 10 }}
          tabBarStyle={{ display: 'none' }}
          activeKey={activeKey}
        >
          <>
            <TabPane tab="车险-订单期账" key="CAR_INSURANCE_ORDER_TERM_BILL">
              <OrderPaymentTermList />
            </TabPane>
            <TabPane tab="车险-车辆期账" key="CAR_INSURANCE_TERM_BILL">
              <CarPaymentTermList />
            </TabPane>
            <TabPane tab="车险-车辆总账" key="CAR_INSURANCE_ORDER_BILL">
              <CarTotalBillList />
            </TabPane>
            <TabPane tab="融租-订单期账" key="FINANCE_LEASE_ORDER_TERM_BILL">
              <FinancePaymentTermList />
            </TabPane>
            <TabPane tab="融租-订单总账" key="FINANCE_LEASE_ORDER_BILL">
              <FinanceTotalBillList />
            </TabPane>
          </>
        </Tabs> */}
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name="businessMng/postLoanMng/after-loan-list">
      <BillManagerList />
    </KeepAlive>
  </>
);
