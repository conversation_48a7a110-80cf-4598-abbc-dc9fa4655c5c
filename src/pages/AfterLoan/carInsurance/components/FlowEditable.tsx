import { EditableProTable, ProFormDependency } from "@ant-design/pro-components";
import type { ProColumns, EditableFormInstance, FormInstance } from "@ant-design/pro-components";
import type { PropsWithChildren } from "react";
import { useDebounceFn } from 'ahooks';
import React, { memo, useEffect, useRef, useState, useImperativeHandle } from "react";
import BigNumber from "bignumber.js";
import RedText from "./RedText";


type DataSourceType = {
  id: React.Key | string;
  flowNo: string;
  payAccountName: string;
  amount: number;
  unusedAmount: number;
  usedAmount: number;
  payaccount: string;
  transactionTime: string;
};

type Props = {
  form: FormInstance;
  name: string;
  selectedData: any[];
  editControRef: React.Ref<any>;
}


const FlowEditable: React.FC<PropsWithChildren<Props>> = (props) => {

  const { form, selectedData, editControRef, name = 'flowNos' } = props;

  const [dataSource, setDataSource] = useState<readonly DataSourceType[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const editableFormRef = useRef<EditableFormInstance<DataSourceType>>(null);

  const [loading, setLoading] = useState(false);


  const { run, cancel } = useDebounceFn((flowNo: string, row: any) => {

    console.log('检查流水号信息', flowNo, row);

    setLoading(true);

    setTimeout(() => {

      editableFormRef.current?.setRowData(row.rowIndex, {
        payAccountName: 'test 123456',
        amount: 100,
        unusedAmount: 100,
        usedAmount: 100,
        payaccount: 'test 123456',
        transactionTime: '2025-01-01 12:00:00',
      })
      setLoading(false);
    }, 1000);
  }, { wait: 500 });


  useImperativeHandle(editControRef, () => ({
    clear: () => {
      setDataSource([]);
      setEditableKeys([]);
      cancel();
    },
  }));

  const getAllRowAmount = (rowData: any) => {
    return new BigNumber(rowData?.reduce((pre, cur) => pre + (cur?.usedAmount || 0), 0)).toFixed(2);
  }


  // console.log('editableRowKeys', editableKeys);
  // console.log('dataSource', dataSource);


  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '帐户',
      dataIndex: 'payAccountName',
      width: '200px',
      valueType: 'select',
      valueEnum: {
        '123': { text: '易人行小贷银行帐户', value: '123' },
        '456': { text: '易人行小贷微信商户号', value: '456' },
        '789': { text: '易人行小贷支付宝商户号', value: '789' },
        '101': { text: '易人行小贷联通商户号', value: '101' },
      },
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '必填',
          },
        ],
      },
    },
    {
      title: '流水号',
      dataIndex: 'flowNo',
      valueType: 'text',
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '必填',
          },
          {
            validator: async (rule, value, callback) => {
              if (value) {
                callback(value);
                return;
              }
              callback('请输入流水号123');
            },
          },
        ],
      },
      fieldProps: (_, row: any) => {
        return {
          onChange: (e: any) => {
            run(e.target.value, row);
          }
        }
      }
    },
    {
      title: '付款方',
      dataIndex: 'payaccount',
      valueType: 'text',
      readonly: true,
    },
    {
      title: '交易时间',
      dataIndex: 'transactionTime',
      valueType: 'dateTime',
      readonly: true,
    },
    {
      title: '到账金额（元）',
      dataIndex: 'amount',
      valueType: 'digit',
      readonly: true,
      fieldProps: {
        precision: 2,
      },
    },
    {
      title: '可用金额（元）',
      dataIndex: 'unusedAmount',
      valueType: 'digit',
      readonly: true,
      fieldProps: {
        precision: 2,
      },
    },
    {
      title: '入账金额（元）',
      dataIndex: 'usedAmount',
      valueType: 'digit',
      fieldProps: {
        precision: 2,
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '必填',
          },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 100,
      render: () => {
        return null;
      },
    },
  ];

  useEffect(() => {
    if (!selectedData.length) return;

    console.log('触发了');
    const formatDate = selectedData.map((item) => ({
      id: item.id,
      payAccountName: item.billNo,
      flowNo: item.flowNo,
      amount: item.amount,
      unusedAmount: item.unusedAmount,
      usedAmount: item.usedAmount,
      payaccount: item.billNo,
      transactionTime: item.transactionTime,
    }))
    setDataSource(formatDate);
    setEditableKeys(formatDate.map((item) => item.id));
    form.setFieldValue(name, formatDate);

  }, [selectedData, form, name]);

  return (
    <EditableProTable<DataSourceType>
      loading={loading}
      columns={columns}
      rowKey="id"
      name={name}
      scroll={{
        x: 'max-content',
      }}
      controlled
      className="flow_editable_list"
      editableFormRef={editableFormRef}
      value={dataSource}
      onChange={(data) => {
        setDataSource(data);
      }}
      headerTitle={
        <ProFormDependency name={['flowNos']}>
          {(values) => {
            const amountTotal = getAllRowAmount(values?.flowNos || [])
            return (
              <div style={{ marginBottom: 16, marginTop: 6 }}>
                合计入账金额：
                <RedText text={amountTotal || '-'} />
                元
              </div>
            );
          }}
        </ProFormDependency>
      }
      recordCreatorProps={{
        creatorButtonText: '新增流水号',
        newRecordType: 'dataSource',
        record: () => ({
          id: (crypto as any).randomUUID().replaceAll('-', '').slice(0, 12), // 生成一个12位的随机数
          // flowNo: '',
          // payAccountName: '',
          // amount: 0,
          // unusedAmount: 0,
          // usedAmount: 0,
          // payaccount: '',
          // transactionTime: '',
        }),
      }}
      editable={{
        type: 'multiple',
        editableKeys,
        onChange: setEditableKeys,
        actionRender: (row, config, defaultDoms) => {
          return [defaultDoms.delete];
        },
        onDelete: async (rowIndex, row) => {

          setDataSource(dataSource.filter((item) => item.id !== row.id));
          setEditableKeys(editableKeys.filter((item) => item !== row.id));
          return true;
        },
      }}
    />
  )
};

export default memo(FlowEditable);