import { EditableProTable, ProFormDependency } from "@ant-design/pro-components";
import type { ProColumns, EditableFormInstance, FormInstance } from "@ant-design/pro-components";
import type { PropsWithChildren } from "react";
import { useDebounceFn } from 'ahooks';
import React, { memo, useEffect, useRef, useState, useImperativeHandle } from "react";
import BigNumber from "bignumber.js";
import { message } from "antd";
import { queryFlowInfo } from "../services";
import RedText from "./RedText";


type DataSourceType = {
  id: React.Key | string;
  flowNo: string;
  payAccountName: string;
  amount: number;
  unusedAmount: number;
  usedAmount: number;
  payaccount: string;
  transactionTime: string;
};

type Props = {
  form: FormInstance;
  name: string;
  selectedData: any[];
  editControRef: React.Ref<any>;
}


const FlowEditable: React.FC<PropsWithChildren<Props>> = (props) => {

  const { form, selectedData, editControRef, name = 'flowNos' } = props;

  const [dataSource, setDataSource] = useState<readonly DataSourceType[]>([]);
  const [editableKeys, setEditableKeys] = useState<React.Key[]>([]);
  const editableFormRef = useRef<EditableFormInstance<DataSourceType>>(null);

  const [loading, setLoading] = useState(false);


  const { run, cancel } = useDebounceFn(async (flowNo: string, row: any) => {
    if (!flowNo || !flowNo.trim()) {
      return;
    }

    console.log('检查流水号信息', flowNo, row);
    setLoading(true);

    try {
      // 获取当前行的账户信息
      const currentRowData = editableFormRef.current?.getRowData?.(row.rowIndex);
      const payAccountName = currentRowData?.payAccountName;

      if (!payAccountName) {
        message.error('请先选择账户');
        setLoading(false);
        return;
      }

      // 调用接口查询流水号信息
      const response = await queryFlowInfo({
        flowNo: flowNo.trim(),
        payAccountName,
      });

      if (response?.data && response.data.length > 0) {
        const flowData = response.data[0];

        // 判断流水类型：假设 flowType 字段表示流水类型，1为收款，2为付款
        // 这里需要根据实际接口返回的字段进行调整
        const flowType = flowData.flowType || flowData.type || flowData.transactionType;

        if (flowType === 2 || flowData.direction === 'OUT' || flowData.amount < 0) {
          // 付款流水
          message.error('该流水号不可使用');
          // 清空其他字段
          if (editableFormRef.current?.setRowData) {
            editableFormRef.current.setRowData(row.rowIndex, {
              payaccount: '',
              amount: 0,
              unusedAmount: 0,
              usedAmount: 0,
              transactionTime: '',
            });
          }
        } else if (flowType === 1 || flowData.direction === 'IN' || flowData.amount > 0) {
          // 收款流水，反显信息
          if (editableFormRef.current?.setRowData) {
            editableFormRef.current.setRowData(row.rowIndex, {
              payaccount: flowData.payAccount || flowData.payerAccount || flowData.fromAccount || '',
              amount: Math.abs(flowData.amount || 0),
              unusedAmount: Math.abs(flowData.unusedAmount || flowData.availableAmount || flowData.amount || 0),
              usedAmount: Math.abs(flowData.unusedAmount || flowData.availableAmount || flowData.amount || 0), // 默认等于可用金额
              transactionTime: flowData.transactionTime || flowData.createTime || '',
            });
          }
        } else {
          message.error('无法识别流水类型');
        }
      } else {
        // 未查到该流水号
        message.error('未查到该流水号');
        // 清空其他字段
        if (editableFormRef.current?.setRowData) {
          editableFormRef.current.setRowData(row.rowIndex, {
            payaccount: '',
            amount: 0,
            unusedAmount: 0,
            usedAmount: 0,
            transactionTime: '',
          });
        }
      }
    } catch (error) {
      console.error('查询流水号失败:', error);
      message.error('查询流水号失败，请稍后重试');
      // 清空其他字段
      if (editableFormRef.current?.setRowData) {
        editableFormRef.current.setRowData(row.rowIndex, {
          payaccount: '',
          amount: 0,
          unusedAmount: 0,
          usedAmount: 0,
          transactionTime: '',
        });
      }
    } finally {
      setLoading(false);
    }
  }, { wait: 500 });


  useImperativeHandle(editControRef, () => ({
    clear: () => {
      setDataSource([]);
      setEditableKeys([]);
      cancel();
    },
  }));

  const getAllRowAmount = (rowData: any) => {
    return new BigNumber(rowData?.reduce((pre, cur) => pre + (cur?.usedAmount || 0), 0)).toFixed(2);
  }


  // console.log('editableRowKeys', editableKeys);
  // console.log('dataSource', dataSource);


  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '帐户',
      dataIndex: 'payAccountName',
      width: '200px',
      valueType: 'select',
      valueEnum: {
        '123': { text: '易人行小贷银行帐户', value: '123' },
        '456': { text: '易人行小贷微信商户号', value: '456' },
        '789': { text: '易人行小贷支付宝商户号', value: '789' },
        '101': { text: '易人行小贷联通商户号', value: '101' },
      },
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '必填',
          },
        ],
      },
    },
    {
      title: '流水号',
      dataIndex: 'flowNo',
      valueType: 'text',
      formItemProps: {
        rules: [
          {
            required: true,
            whitespace: true,
            message: '必填',
          },
          {
            validator: async (_, value) => {
              if (!value || !value.trim()) {
                throw new Error('请输入流水号');
              }
            },
          },
        ],
      },
      fieldProps: (_, row: any) => {
        return {
          onChange: (e: any) => {
            run(e.target.value, row);
          }
        }
      }
    },
    {
      title: '付款方',
      dataIndex: 'payaccount',
      valueType: 'text',
      readonly: true,
    },
    {
      title: '交易时间',
      dataIndex: 'transactionTime',
      valueType: 'dateTime',
      readonly: true,
    },
    {
      title: '到账金额（元）',
      dataIndex: 'amount',
      valueType: 'digit',
      readonly: true,
      fieldProps: {
        precision: 2,
      },
    },
    {
      title: '可用金额（元）',
      dataIndex: 'unusedAmount',
      valueType: 'digit',
      readonly: true,
      fieldProps: {
        precision: 2,
      },
    },
    {
      title: '入账金额（元）',
      dataIndex: 'usedAmount',
      valueType: 'digit',
      fieldProps: (_, record: any) => ({
        precision: 2,
        max: record?.unusedAmount || undefined,
        onChange: (value: number) => {
          // 确保入账金额不超过可用金额
          if (record?.unusedAmount && value > record.unusedAmount) {
            message.warning('入账金额不能超过可用金额');
          }
        },
      }),
      formItemProps: {
        rules: [
          {
            required: true,
            message: '必填',
          },
          {
            validator: async (_, value) => {
              if (value !== undefined && value !== null) {
                if (Number(value) <= 0) {
                  throw new Error('入账金额必须大于0');
                }
              }
            },
          },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 100,
      render: () => {
        return null;
      },
    },
  ];

  useEffect(() => {
    if (!selectedData.length) return;

    console.log('触发了');
    const formatDate = selectedData.map((item) => ({
      id: item.id,
      payAccountName: item.billNo,
      flowNo: item.flowNo,
      amount: item.amount,
      unusedAmount: item.unusedAmount,
      usedAmount: item.usedAmount,
      payaccount: item.billNo,
      transactionTime: item.transactionTime,
    }))
    setDataSource(formatDate);
    setEditableKeys(formatDate.map((item) => item.id));
    form.setFieldValue(name, formatDate);

  }, [selectedData, form, name]);

  return (
    <EditableProTable<DataSourceType>
      loading={loading}
      columns={columns}
      rowKey="id"
      name={name}
      scroll={{
        x: 'max-content',
      }}
      controlled
      className="flow_editable_list"
      editableFormRef={editableFormRef}
      value={dataSource}
      onChange={(data) => {
        setDataSource(data);
      }}
      headerTitle={
        <ProFormDependency name={['flowNos']}>
          {(values) => {
            const amountTotal = getAllRowAmount(values?.flowNos || [])
            return (
              <div style={{ marginBottom: 16, marginTop: 6 }}>
                合计入账金额：
                <RedText text={amountTotal || '-'} />
                元
              </div>
            );
          }}
        </ProFormDependency>
      }
      recordCreatorProps={{
        creatorButtonText: '新增流水号',
        newRecordType: 'dataSource',
        record: () => ({
          id: (crypto as any).randomUUID().replaceAll('-', '').slice(0, 12), // 生成一个12位的随机数
          flowNo: '',
          payAccountName: '',
          amount: 0,
          unusedAmount: 0,
          usedAmount: 0,
          payaccount: '',
          transactionTime: '',
        }),
      }}
      editable={{
        type: 'multiple',
        editableKeys,
        onChange: setEditableKeys,
        actionRender: (_, __, defaultDoms) => {
          return [defaultDoms.delete];
        },
        onDelete: async (_, row) => {
          setDataSource(dataSource.filter((item) => item.id !== row.id));
          setEditableKeys(editableKeys.filter((item) => item !== row.id));
          return true;
        },
      }}
    />
  )
};

export default memo(FlowEditable);