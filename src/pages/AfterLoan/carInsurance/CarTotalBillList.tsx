/**
 * 车险 - 车辆总账 支持跨页勾选和虚拟列表
 */
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { RePaymentQrViewer } from '@/components/QrViewer/RepaymentQrViewer';
import { filterProps, removeBlankFromObject } from '@/utils/tools'; // @ts-ignore
import type { ActionType, ProFormInstance } from '@ant-design/pro-components'; // @ts-ignore
import { ProTable } from '@ant-design/pro-components';
import { Button, message } from 'antd';
import BigNumber from 'bignumber.js';
import React, { useEffect, useRef, useState } from 'react';
import { useAccess, useModel } from 'umi';
import { VList } from 'virtuallist-antd';
import { getCarTotalBillListColumn } from './columns/CarTotalBillListColumns';
import EarlySettle from './components/EarlySettle';
import OnlineRepayment from './components/OnlineRepayment';
import { billExport, billInfo, billList, getReferenceAmount } from './services';
import type { IbillListItem, IbillListParams } from './types';
import { IdimensionEnCode } from './types';

type Props = {};
const CarTotalBillList: React.FC<Props> = () => {
  const formRef = useRef<ProFormInstance>();
  const access = useAccess();
  const [selectedRowKeys, setSelectedRowKeys] = useState<Record<string, string[]>>({});
  // 无论分页怎么变化 只要含有key 就会被勾选
  const [allSelectedRowKeys, setAllSelectedRowKeys] = useState<string[]>([]);
  const [selectedRows, setSelectedRows] = useState<Record<string, IbillListItem[]>>({});
  const [allSelectedRows, setAllSelectedRows] = useState<IbillListItem[]>([]);
  const { initialState = {} }: any = useModel<any>('@@initialState');
  const { selectedData } = useModel('AccountCheckMng.shared');

  const { currentUser = {} } = initialState;
  const { channelCode, channelLevel } = currentUser;
  const [currentPage, setCurrentPage] = useState(1);

  const [isBigData, setIsBigData] = useState(false); // 是否是大数据量 大数据量下会卡顿 所以启用虚拟列表

  const actionRef = useRef<ActionType>();

  useEffect(() => {
    setAllSelectedRows(
      Array.from(
        new Map(
          Object.values(selectedRows)
            .flat(2)
            ?.map((item) => [item.id, item]),
        ).values(),
      ) as any,
    );
    setAllSelectedRowKeys([...new Set(Object.values(selectedRowKeys).flat(2))] as any);
  }, [selectedRows, selectedRowKeys]);

  function caculate(rows: any) {
    const amount = rows.reduce((pre: any, cur: any) => {
      return new BigNumber(cur.totalAmountUnpaid || 0).plus(pre);
    }, 0);
    const applyAmount = Number(new BigNumber(amount));
    return applyAmount;
  }

  function getSelectedAlertRender() {
    const amount = caculate(allSelectedRows);
    return allSelectedRows.length ? (
      <div style={{ padding: '0 24px', display: 'flex', justifyContent: 'space-between' }}>
        <div>
          已经选择 {allSelectedRows.length} 条数据, 正常还款情况下剩余未还总额
          <span style={{ color: 'red' }}> {amount} 元</span>
        </div>
        <a
          onClick={() => {
            setAllSelectedRows([]);
            setSelectedRowKeys({});
            setSelectedRows({});
          }}
        >
          取消选择
        </a>
      </div>
    ) : null;
  }

  // 线上结清相关
  const [onlineRepaymentVisible, setOnlineRepaymentVisible] = useState(false);
  const [onlineRepaymentChecking, setOnlineRepaymentChecking] = useState(false);
  const [onlineBillInfoData, setOnlineBillInfoData] = useState<any>();
  const [planRepayAmount, setPlanRepayAmount] = useState<any>({
    // 还款至下一期结清
    repayNextTermAmount: 0,
    // 还款至逾期结清
    repayOverdueAmount: 0,
  });
  // 线上结清检查
  const checkOnlineRepayment = async () => {
    try {
      // 如果存在已勾选的流水号，则不允许提交线上还款
      if (selectedData.hasSelected) {
        message.error('存在已勾选的流水号，请先取消勾选');
        return;
      }
      setOnlineRepaymentChecking(true);
      console.log('checkOnlineRepayment', allSelectedRows);
      if (!allSelectedRows?.length) {
        message.error('还未勾选任何数据');
        setOnlineRepaymentChecking(false);
        return;
      }
      //
      const billNoList: any = allSelectedRows?.map((item) => item?.billNo);
      const data = await billInfo({
        billNoList,
        dimension: IdimensionEnCode.SUBJECT_MATTER_BILL,
        secondaryClassification: '0303',
        settleType: false,
        // 线上还款
        onlineRepay: true,
      });
      // 普通还款还需要请求计划还款金额推荐值
      // const orderNoList: any = allSelectedRows?.map((item) => item?.orderNo);
      // const subjectMatterNoList: any = allSelectedRows?.map((item) => item?.subjectMatterNo);
      //
      const order2subjectMatterNoMap = {};
      allSelectedRows?.forEach((item) => {
        if (order2subjectMatterNoMap[item?.orderNo]) {
          order2subjectMatterNoMap[item?.orderNo]?.push(item?.subjectMatterNo);
        } else {
          order2subjectMatterNoMap[item?.orderNo] = [item?.subjectMatterNo];
        }
      });
      //
      const planData = await getReferenceAmount({
        billNoList,
        // orderNoList,
        // subjectMatterNoList,
        order2subjectMatterNoMap,
      }).catch(() => {});
      console.log('planData', planData);
      if (planData && planData?.data) {
        const { repayNextTermAmount, repayOverdueAmount } = planData?.data || {};
        setPlanRepayAmount({ repayNextTermAmount, repayOverdueAmount });
      }
      setOnlineBillInfoData(data);
      setOnlineRepaymentVisible(true);
      setOnlineRepaymentChecking(false);
    } catch (error) {
    } finally {
      setOnlineRepaymentChecking(false);
    }
  };
  // 车险线上结清二维码
  // 还款二维码
  const [qrVisible, setQrVisible] = useState(false);
  const [qrData, setQrData] = useState<any>({});

  return (
    <>
      <ProTable
        search={{
          defaultCollapsed: false,
          labelWidth: 'auto',
        }}
        actionRef={actionRef}
        columns={getCarTotalBillListColumn({ actionRef, channelCode, access, channelLevel })}
        request={async (values) => {
          const { current = 1, pageSize = 20, billNo, termList } = values;
          const params: IbillListParams = {
            ...values,
            dimension: IdimensionEnCode.SUBJECT_MATTER_BILL,
            current,
            pageSize,
            secondaryClassification: '0303',
            billNoList: billNo ? [billNo] : undefined,
            termList: termList ? [termList] : undefined,
          };
          return billList(removeBlankFromObject(filterProps(params)));
        }}
        expandable={{}}
        rowKey="id"
        scroll={isBigData ? { y: window.innerHeight - 224 } : { x: 'max-content' }}
        components={
          isBigData
            ? VList({
                height: window.innerHeight - 224,
              })
            : null
        }
        pagination={{
          pageSizeOptions: [10, 20, 50, 100],
          showSizeChanger: true,
          onShowSizeChange: (currentPage, pageSize) => {
            setCurrentPage(currentPage);
            if (pageSize >= 500) {
              setIsBigData(true);
            } else {
              setIsBigData(false);
            }
          },
        }}
        tableExtraRender={() => {
          return getSelectedAlertRender();
        }}
        rowSelection={{
          selectedRowKeys: allSelectedRowKeys,
          onChange: (_selectedRowKeys, _selectedRowsArg) => {
            setSelectedRowKeys({ ...selectedRowKeys, [currentPage]: _selectedRowKeys });
            setSelectedRows({ ...selectedRows, [currentPage]: _selectedRowsArg });
          },
        }}
        tableAlertOptionRender={false}
        tableAlertRender={false}
        formRef={formRef}
        toolBarRender={() => {
          return [
            access.hasAccess('bill_apply_export_postLoanMng_afterLoanList') && (
              <AsyncExport
                getSearchDataTotal={async () => {
                  const values = formRef.current?.getFieldsFormatValue?.();
                  const { billNo } = values;
                  const params: IbillListParams = {
                    ...values,
                    billNoList: billNo ? [billNo] : undefined,
                    dimension: IdimensionEnCode.SUBJECT_MATTER_BILL,
                    current: 1,
                    pageSize: 1,
                    secondaryClassification: '0303',
                  };
                  const data = await billList(removeBlankFromObject(filterProps(params)));
                  return data?.total;
                }}
                getSearchParams={() => {
                  const values = formRef.current?.getFieldsFormatValue?.();
                  const { current = 1, pageSize = 20, billNo } = values;
                  const params: IbillListParams = {
                    ...values,
                    billNoList: billNo ? [billNo] : undefined,
                    dimension: IdimensionEnCode.SUBJECT_MATTER_BILL,
                    current,
                    pageSize,
                    secondaryClassification: '0303',
                  };
                  return removeBlankFromObject(filterProps(params));
                }}
                getSelectedParams={() => {
                  const values = formRef.current?.getFieldsFormatValue?.();
                  return {
                    billNoList: allSelectedRows.map((item) => item.billNo),
                    dimension: IdimensionEnCode.SUBJECT_MATTER_BILL,
                    secondaryClassification: '0303',
                    channelCode: values?.channelCode,
                  };
                }}
                getSelectedTotal={() => {
                  return allSelectedRows.length;
                }}
                exportAsync={billExport}
                taskCode={[ItaskCodeEnValueEnum.REPAY_CAR_INSURANCE_BILL_TOTAL]}
              />
            ),
            access.hasAccess('biz_billList_earlySettlement_button') && (
              <EarlySettle
                selectedRows={allSelectedRows}
                dimension={IdimensionEnCode.SUBJECT_MATTER_BILL}
                actionRef={actionRef}
              />
            ),
            access.hasAccess('biz_billList_insurance_onlineRepay') && (
              <Button
                type="primary"
                key={'onlineRepayment'}
                loading={onlineRepaymentChecking}
                onClick={checkOnlineRepayment}
              >
                线上还款/结清
              </Button>
            ),
          ];
        }}
      />
      {onlineRepaymentVisible && (
        <OnlineRepayment
          selectedRows={allSelectedRows}
          open={onlineRepaymentVisible}
          setOpen={(val: boolean) => {
            console.log('setOpen', val);
            setOnlineRepaymentVisible(val);
          }}
          dimension={IdimensionEnCode.SUBJECT_MATTER_BILL}
          //
          onlineBillInfoData={onlineBillInfoData}
          planAmount={planRepayAmount}
          // 二维码
          setQrData={setQrData}
          setQrVisible={setQrVisible}
          actionRef={actionRef}
        />
      )}
      {/* 车险还款二维码 */}
      {qrVisible && (
        <RePaymentQrViewer
          data={qrData}
          qrVisible={qrVisible}
          // availableRepay={['微信']}
          handleQRVisible={setQrVisible}
          title="还款二维码"
        />
      )}
    </>
  );
};

export default CarTotalBillList;
