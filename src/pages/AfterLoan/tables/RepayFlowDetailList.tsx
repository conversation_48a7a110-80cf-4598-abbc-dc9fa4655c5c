/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-09-20 17:08:30
 * @LastEditors: oak.yang <EMAIL>
 * @LastEditTime: 2025-02-24 18:04:33
 * @FilePath: /lala-finance-biz-web/src/pages/AfterLoan/repay-regist.tsx
 * @Description: repay-regist
 */
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import type { SECONDARY_CLASSIFICATION } from '@/enums';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import { isChannelStoreUser } from '@/utils/utils';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Link, useAccess } from '@umijs/max';
import { But<PERSON>, message } from 'antd';
import dayjs from 'dayjs';
import React, { useRef } from 'react';
import { recordingExportAsync, repayRegistList } from '../services';
import type { RepayFlowDetailItem } from '../types';

type Props = {
  classification: string;
  secondaryClassification?: keyof typeof SECONDARY_CLASSIFICATION;
};

const RepayFlowDetailList: React.FC<Props> = (props) => {
  const { classification, secondaryClassification } = props;
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  // 有一些额外的请求请求参数的设置
  const getChannelStoreRequestParams = (access: any) => {
    const params: any = {};
    if (isChannelStoreUser(access)) {
      // 渠道类型
      if (access.currentUser.channelType) {
        params.channelTypes = access.currentUser.channelType;
      }
      // 渠道
      if (access.currentUser?.channelCode) params.channelIds = [access.currentUser.channelCode];
      // 门店
      if (access.currentUser?.extSource?.storeId)
        params.storeIds = [access.currentUser.extSource.storeId];
    }
    return params;
  };

  const columns: ProColumns<RepayFlowDetailItem>[] = [
    {
      title: '流水号',
      dataIndex: 'bankSerialNo',
      hideInTable: true,
    },
    {
      title: '银行/商户流水号',
      dataIndex: 'merchantId',
      search: false,
      render: (_, record) => {
        if (record.merchantId) {
          return (
            <Link to={`/businessMng/account-check-detail?flowNo=${record.merchantId}`}>
              {record.merchantId}
            </Link>
          );
        }
        return '-';
      },
    },
    {
      title: '帐户',
      dataIndex: 'account',
      search: false,
    },
    {
      title: '还款编号',
      dataIndex: 'repayNo',
      search: false,
    },
    {
      title: '期数',
      dataIndex: 'termDetail',
      search: false,
    },
    {
      title: '用户名称',
      dataIndex: 'accountName',
    },
    {
      title: '用户ID',
      dataIndex: 'accountNumber',
      search: false,
    },
    {
      title: '车牌号',
      dataIndex: 'carNo',
    },
    {
      dataIndex: 'vin',
      title: '车架号',
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
      order: 1,
    },
    {
      title: '投保主体',
      dataIndex: 'insuredSubject',
      search: false,
    },
    {
      title: '入账场景',
      valueType: 'select',
      valueEnum: {
        '1': '线下还款/结清',
        '2': '二维码还款',
        '3': '线上退保回款',
        // '4': '线下退保回款',
        '5': '减保回款',
      },
      dataIndex: 'scene',
    },
    {
      title: '入账总额',
      valueType: 'money',
      dataIndex: 'totalAmount',
      search: false,
    },
    {
      title: '入账本金',
      valueType: 'money',
      dataIndex: 'principalAmount',
      search: false,
    },
    {
      title: '入账利息',
      valueType: 'money',
      dataIndex: 'interestAmount',
      search: false,
    },
    {
      title: '入账罚息',
      valueType: 'money',
      dataIndex: 'penaltyAmount',
      search: false,
    },
    {
      title: '入账其他费用',
      valueType: 'money',
      dataIndex: 'otherAmount',
      search: false,
    },
    {
      title: '入账时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '入账日期',
      dataIndex: 'createDate',
      valueType: 'dateRange',
      initialValue: [dayjs().subtract(1, 'year'), dayjs()],
      fieldProps: {
        allowClear: false,
      },
      search: {
        transform: (value: any) => {
          return {
            createDateStart: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
            createDateEnd: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
          };
        },
      },
      hideInTable: true,
    },
  ];

  async function getSearchDataTotal() {
    const searchParams = removeBlankFromObject(
      filterProps(formRef.current?.getFieldsFormatValue?.()),
    );
    const data = await repayRegistList({
      classification,
      secondaryClassification,
      ...searchParams,
    });
    return data?.total;
  }
  return (
    <div>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        rowKey="repayNo"
        search={{ labelWidth: 100, defaultCollapsed: false }}
        scroll={{ x: 'max-content' }}
        onSubmit={() => {
          if (!classification || !secondaryClassification) {
            // 只要有一个不存在 就不能发请求
            // 一级分类切换 由于联动 会清空二级分类
            message.error('产品一级和二级分类必填');
          }
        }}
        request={(params) => {
          if (!classification || !secondaryClassification) {
            // 只要有一个不存在 就不能发请求
            // 一级分类切换 由于联动 会清空二级分类
            message.error('产品一级和二级分类必填');
            return {} as any;
          }
          return repayRegistList(
            removeBlankFromObject(
              filterProps({
                ...params,
                classification,
                secondaryClassification,
              }),
            ),
          );
        }}
        toolBarRender={() => {
          return [
            // access.hasAccess('repay_recording_export_postLoanMng_afterLoanList') && (
            <AsyncExport
              key={`${classification}-${secondaryClassification}-export`}
              getSearchDataTotal={getSearchDataTotal}
              getSearchParams={() => {
                //getFieldsFormatValue 是无法携带 ProTable 的 params
                const params = formRef.current?.getFieldsFormatValue?.(true);
                return removeBlankFromObject(
                  filterProps({
                    ...params,
                    classification,
                    secondaryClassification,
                  }),
                );
              }}
              trigger={
                <Button
                  type="primary"
                  onClick={(e) => {
                    if (!classification || !secondaryClassification) {
                      // 只要有一个不存在 就不能发请求
                      // 一级分类切换 由于联动 会清空二级分类
                      message.error('产品一级和二级分类必填');
                      e.stopPropagation();
                    }
                  }}
                >
                  导出
                </Button>
              }
              exportAsync={recordingExportAsync}
              taskCode={[ItaskCodeEnValueEnum.ACCOUNT_CHECK_BILL_REPAY_DETAIL_EXPORT]}
            />,
            // ),
          ];
        }}
        dateFormatter="string"
        columns={columns}
      />
    </div>
  );
};

export default RepayFlowDetailList;
