/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2023-02-01 10:41:22
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-11-22 10:37:33
 * @FilePath: /lala-finance-biz-web/src/pages/AfterLoan/repay-detail.tsx
 * @Description: AfterLoan
 */
import { ADVANCED_STATUS, CLASSIFICATION, SECONDARY_CLASSIFICATION } from '@/enums';
import { getAllChannelNameEnum } from '@/services/enum';
import {
  getUuid,
  isCarInsuranceStoreUser,
  isChannelOrExternal,
  isChannelStoreUser,
} from '@/utils/utils';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Link, useAccess, useModel } from '@umijs/max';
import { message } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { mapStatusZh } from '../../BusinessCashMng/const';
import {
  getAfterLoanList,
  // afterLoanExcel,
  getStoreAndApplyCityAll,
  repayDetailExportAsync,
} from '../services';
import type { AfterLoanListItem } from '../types/index';
import { secondaryClassificationEnCodeMap } from '../types/index';
import { getChannelStoreRequestParams, setFormValuesForChannelStore } from '../utils';
// import LoadingButton from '@/components/LoadingButton';
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import { Button } from 'antd';
import { getChannelInfo, getProductNameEnum } from '../../CarInsurance/services';
import { LEVEL } from '../../CarInsurance/type';

type Props = {
  classification: string;
  secondaryClassification?: keyof typeof SECONDARY_CLASSIFICATION;
};

const DetailList: React.FC<Props> = (props) => {
  const { classification, secondaryClassification } = props;
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();
  const [isCarInsurance, setIsCarInsurance] = useState(false);
  const [secondProduct, setSecondProduct] = useState(
    isChannelStoreUser(access) ? 'FINANCE_LEASE' : '',
  ); //  二级分类
  // 所有的
  const [productOptions, setProductOptions] = useState<{ value: string; label: string }[]>([]);
  // 不同二级分类下的
  const [currentProductOptions, setCurrentProductOptions] = useState<
    { value: string; label: string }[]
  >([]);

  const { initialState = {} } = useModel<any>('@@initialState');
  const { currentUser = {} } = initialState;
  const { channelCode, channelLevel } = currentUser;

  useEffect(() => {
    //融租渠道用户不需要产品名称搜索
    if (!isChannelStoreUser(access) && secondaryClassification) {
      // 获取 设置 所有的产品选项
      getProductNameEnum(secondaryClassification).then((res) => {
        setProductOptions(res);
      });
    }
  }, [secondaryClassification, access]);

  useEffect(() => {
    setIsCarInsurance(secondaryClassification === 'CAR_INSURANCE');
    if (secondaryClassification) {
      setSecondProduct(secondaryClassification);
    }
  }, [secondaryClassification]);

  // 当一级和二级分类发生变化 且都存在时 触发提交
  // 一级和二级发生变化，对应产品名称也会变化，所以要情况产品名称，让用户重新选择
  useEffect(() => {
    formRef.current?.setFieldValue('productCode', undefined);
    formRef.current?.setFieldValue('channelIds', undefined);
    if (classification && secondaryClassification) {
      formRef.current?.submit();
    }
  }, [classification, secondaryClassification]);

  // 设置二级分类对应的产品名称 二级分类不存在设置为空[]
  useEffect(() => {
    if (secondaryClassification && productOptions.length) {
      const twoCode = secondaryClassificationEnCodeMap[secondaryClassification];
      setCurrentProductOptions(
        productOptions.filter((product) => product.value.substring(0, 4) === twoCode),
      );
    } else {
      setCurrentProductOptions([]);
    }
  }, [secondaryClassification, productOptions]);

  // 缓存
  const getStoreAndApplyCityAllMemo = useMemo(async () => {
    return await getStoreAndApplyCityAll();
  }, []);
  // const getAllChannelNameEnumMemo = useMemo(async () => {
  //   return await getAllChannelNameEnum();
  // }, []);

  // const getCarInsuranceChannelNameMemo = useMemo(async () => {
  //   getChannelInfo({ channelCode, channelLevel }).then((res) => {
  //     console.log(res);
  //     return res.map((item) => {
  //       return {
  //         value: item.channelCode,
  //         label: item.channelName,
  //         title: item.channelName,
  //       };
  //     });
  //   });
  // }, []);
  const getChannelListSummarize = useMemo(async () => {
    console.log(secondaryClassification);
    if (secondaryClassification === 'CAR_INSURANCE') {
      return await getChannelInfo({ channelCode, channelLevel }).then((res) => {
        return res.map((item) => ({
          value: item.channelCode,
          label: item.channelName,
          title: item.channelName,
        }));
      });
    } else if (secondaryClassification === 'FINANCE_LEASE') {
      return await getAllChannelNameEnum();
    } else {
      return [];
    }
  }, [secondaryClassification, channelCode, channelLevel]);
  console.log('access', access, secondaryClassification);

  // leaseStoreUser    融租门店用户 有一个固定的门店 access?.currentUser?.extSource?.storeId
  // leaseChannelUser  融租渠道用户 是固定的渠道
  // 对于以上用户需要初始化表单值
  useEffect(() => {
    setFormValuesForChannelStore(access, formRef);
  }, [access]);

  function getToSearch(record: AfterLoanListItem) {
    const { productCode, orderNo } = record;
    // 还款管理 - 车险的详情
    const carInsurceDetail = `/businessMng/postLoanMng/car-insurance/detail?orderNo=${orderNo}`;
    // 以前老的逻辑
    const oldDetail = `/businessMng/postLoanMng/after-loan-detail?orderNo=${
      record.orderNo
    }&productCode=${record.productCode}&termDetail=${
      record.termDetail ? record.termDetail.split('/')[0] : 0
    }`;
    // 如果是车险的 则跳转车险的还款管理的详情
    const toSearch = productCode?.slice(0, 4) === '0303' ? carInsurceDetail : oldDetail;
    return toSearch;
  }

  const columns: ProColumns<AfterLoanListItem>[] = [
    {
      title: '还款编号',
      dataIndex: 'repayNo',
      search: false,
      render: (_, record) => {
        let dom: React.ReactNode = '-';
        if (access.hasAccess('repayment_detail_postLoanMng_afterLoanList')) {
          dom = <Link to={getToSearch(record)}>{record.repayNo}</Link>;
        } else {
          dom = (
            <Button disabled type="link">
              {record.repayNo}
            </Button>
          );
        }
        return dom;
      },
    },
    {
      title: '期数',
      dataIndex: 'termDetail',
      search: false,
    },
    {
      title: '订单号',
      dataIndex: 'orderNo',
    },

    {
      title: '用户名称',
      dataIndex: 'accountName',
    },
    {
      title: '用户ID',
      dataIndex: 'accountNumber',
    },
    {
      title: '产品一级分类',
      dataIndex: 'classification',
      initialValue: 'FINANCE_LEASE',
      valueType: 'select',
      valueEnum: CLASSIFICATION,
      search: false,
      // fieldProps: {
      //   allowClear: false,
      //   onChange(value: string) {
      //     if (value === 'SMALL_LOAN') {
      //       formRef.current?.setFieldValue('secondaryClassification', 'CAR_INSURANCE');
      //       setIsCarInsurance(true);
      //       setIsAllowClear(false);
      //     } else if (value === 'FINANCE_LEASE') {
      //       formRef.current?.setFieldValue('secondaryClassification', 'FINANCE_LEASE');
      //       setIsAllowClear(false);
      //     } else {
      //       setIsAllowClear(true);
      //     }
      //   },
      //   disabled: isChannelStoreUser(access),
      // },
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondaryClassification',
      valueType: 'select',
      initialValue: 'FINANCE_LEASE', //  融租渠道用户筛选小圆车融',
      valueEnum: SECONDARY_CLASSIFICATION,
      hideInTable: true,
      search: false,
      // fieldProps: {
      //   onChange: (value: string) => {
      //     setIsCarInsurance(value === 'CAR_INSURANCE');
      //     setSecondProduct(value);
      //   },
      //   allowClear: isAllowClear,
      //   disabled: isChannelStoreUser(access),
      // },
    },

    {
      title: '车牌号',
      dataIndex: 'licensePlateNo',
      hideInTable: !isCarInsurance,
      search: false,
    },
    {
      title: '车架号',
      dataIndex: 'vin',
      hideInTable: !isCarInsurance,
      search: false,
    },
    {
      title: '产品名称',
      dataIndex: 'productCodeList',
      // valueEnum: productNameList,
      search: !isChannelStoreUser(access) as any, //  渠道门店帐号不需展示产品名称筛选
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        options: currentProductOptions,
      },
      // fieldProps: {
      //   showSearch: true,

      //   showArrow: true,
      //   disabled:
      //     (isChannelStoreUser(access) && !!access.currentUser?.channelCode) ||
      //     (isCarInsuranceStoreUser(access) && channelLevel === LEVEL.SECOND_CHANNEL),
      //   optionFilterProp: 'label',
      //   filterOption: (input: string, option: { label: string }) =>
      //     option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      // },
      hideInTable: true,
    },
    {
      title: '投保主体',
      dataIndex: 'insuranceSubject',
      search: false,
      hideInTable: !isCarInsurance,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '期限',
      dataIndex: 'repayTerm',
      valueEnum: {
        1: '固定还款日',
      },
      search: false,
    },
    {
      title: '资金渠道',
      dataIndex: 'loanChannelCode',
      search: false,
      hideInTable: isCarInsurance,
    },
    {
      title: '融租渠道类型',
      dataIndex: 'leaseChannelType',
      search: false,
      hideInTable: isCarInsurance,
    },
    {
      title: '渠道名称',
      dataIndex: 'leaseChannelName',
      search: false,
    },
    {
      title: '门店',
      dataIndex: 'leaseStoreName',
      search: false,
      hideInTable: secondProduct !== 'FINANCE_LEASE',
    },
    {
      title: '应还款总额',
      dataIndex: 'amountDue',
      search: false,
    },
    {
      title: '应还本金',
      dataIndex: 'principalRepayable',
      search: false,
    },
    {
      title: '应还利息',
      dataIndex: 'principalInterest',
      search: false,
    },
    {
      title: '已还总额',
      dataIndex: 'totalPaid',
      search: false,
    },
    {
      title: '已还本金',
      dataIndex: 'repayPrincipal',
      search: false,
    },
    {
      title: '已还利息',
      dataIndex: 'repayInterest',
      search: false,
    },
    {
      title: '已还逾期罚息',
      dataIndex: 'repayInterestPenalty',
      search: false,
    },
    {
      title: '减免金额',
      dataIndex: 'derateMoney',
      search: false,
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDay',
      search: false,
    },
    {
      title: '逾期罚息',
      dataIndex: 'overduePenaltyInterest',
      search: false,
    },
    {
      title: '费用',
      dataIndex: 'cost',
      search: false,
    },
    { title: '已还费用', dataIndex: 'repayCost', search: false },
    {
      title: '应还款日',
      dataIndex: 'repayTime',
      valueType: 'dateRange',
      fieldProps: {
        ranges: {
          待确定: [dayjs('2220-01-01'), dayjs('2220-01-01')],
        },
        format: (value: dayjs.Dayjs) => {
          if (value.format('YYYY-MM-DD') === '2220-01-01') {
            return `待确定`;
          }
          return value.format('YYYY-MM-DD');
        },
      },
      search: {
        transform: (value: any) => {
          return {
            repayStartTime: value[0] === '待确定' ? '2220-01-01' : `${value[0]}`,
            repayEndTime: value[1] === '待确定' ? '2220-01-01' : `${value[1]}`,
          };
        },
      },
      render: (_, record) => {
        return record.repayTime ? record.repayTime : '待确定';
      },
    },
    {
      title: '实际结清日期',
      dataIndex: 'clearDate',
      search: false,
    },
    {
      title: '还款状态',
      dataIndex: 'status',
      valueEnum: mapStatusZh,
    },
    // {
    //   title: '易人行垫付',
    //   dataIndex: 'advancedStatus',
    //   key: 'advancedStatus',
    //   search: false,
    //   valueEnum: ADVANCED_STATUS,
    //   hidden: isChannelStoreUser(access),
    // },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      search: {
        transform: (value: any) => {
          return {
            createStartTime: `${value[0]}`,
            createEndTime: `${value[1]}`,
          };
        },
      },
      render(_, record) {
        return record.createTime;
      },
    },
    {
      title: '易人行逾期垫付',
      dataIndex: 'advancedStatus',
      search: false,
      hideInTable: isChannelOrExternal(access),
      valueEnum: ADVANCED_STATUS,
    },
    {
      title: '垫付金额',
      dataIndex: 'advanceAmount',
      search: false,
      hideInTable: isChannelOrExternal(access),
    },
    {
      title: '首次发起垫付时间',
      dataIndex: 'advanceFirstTime',
      search: false,
      hideInTable: isChannelOrExternal(access),
    },
    {
      title: '垫付完成时间',
      dataIndex: 'advanceSucceedTime',
      search: false,
      hideInTable: isChannelOrExternal(access),
    },
    {
      title: '渠道名称',
      dataIndex: 'channelIds',
      params: { key: getUuid() },
      valueType: 'select',
      //车险或者融租才有渠道
      search:
        secondProduct === 'FINANCE_LEASE' || secondProduct === 'CAR_INSURANCE' ? undefined : false, //车险和车融展示渠道
      request: async () => getChannelListSummarize,
      hideInTable: true,
      //二级车险渠道只有自己，禁用渠道同时，选中自己
      initialValue:
        (isCarInsuranceStoreUser(access) && channelLevel === LEVEL.SECOND_CHANNEL) ||
        (isChannelStoreUser(access) && !!access.currentUser?.channelCode)
          ? [channelCode]
          : undefined,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        disabled:
          (isChannelStoreUser(access) && !!access.currentUser?.channelCode) ||
          (isCarInsuranceStoreUser(access) && channelLevel === LEVEL.SECOND_CHANNEL),
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '门店',
      dataIndex: 'storeIds',
      params: { key: 'storeIds_detail_list' },
      hideInTable: true,
      search: secondProduct !== 'FINANCE_LEASE' ? false : undefined,
      request: () => {
        return getStoreAndApplyCityAllMemo.then((res) => {
          return (
            res?.data?.storeList?.map((item: { storeName: string; id: string }) => {
              return { value: item.id?.toString(), label: item.storeName };
            }) || []
          );
        });
      },
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        disabled: isChannelStoreUser(access) && !!access.currentUser?.extSource?.storeId,
        showArrow: true,
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '申请城市',
      dataIndex: 'applyCityIds',
      hideInTable: true,
      search: secondProduct !== 'FINANCE_LEASE' ? false : undefined,
      request: () => {
        return getStoreAndApplyCityAllMemo.then((res) => {
          return (
            res?.data?.applyCityList?.map((item: { saleCity: string; saleCityCode: string }) => {
              return { value: item.saleCityCode, label: item.saleCity };
            }) || []
          );
        });
      },
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        showArrow: true,
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '操作',
      dataIndex: 'option',
      valueType: 'option',
      fixed: 'right',
      width: 80,
      render: (_, record) => {
        return (
          <>
            <Link to={getToSearch(record)}>查看详情</Link>
          </>
        );
      },
    },
  ];
  async function getSearchDataTotal() {
    const searchParams = removeBlankFromObject(
      filterProps(formRef.current?.getFieldsFormatValue?.()),
    );
    const data = await getAfterLoanList({
      ...searchParams,
      classification,
      secondaryClassification,
    });
    return data?.total;
  }
  return (
    <ProTable<AfterLoanListItem>
      actionRef={actionRef}
      formRef={formRef}
      rowKey="repayNo"
      search={{ defaultCollapsed: false }}
      scroll={{ x: 'max-content' }}
      onSubmit={() => {
        if (!classification || !secondaryClassification) {
          // 只要有一个不存在 就不能发请求
          // 一级分类切换 由于联动 会清空二级分类
          message.error('产品一级和二级分类必填');
        }
      }}
      params={{ classification, secondaryClassification }}
      request={(params) => {
        if (!classification || !secondaryClassification) {
          // 只要有一个不存在 就不能发请求
          // 一级分类切换 由于联动 会清空二级分类
          // message.error("产品一级和二级分类必填")
          return {} as any;
        }
        // 获取融租渠道和门店用户的请求参数
        const channelStoreParams = getChannelStoreRequestParams(access);
        // const params1 = filterProps({ ...channelStoreParams, ...params });
        return getAfterLoanList(
          removeBlankFromObject(
            filterProps({
              ...channelStoreParams,
              ...params,
              classification,
              secondaryClassification,
            }),
          ),
        );
      }}
      toolBarRender={() => {
        return [
          access.hasAccess('repayment_detail_export_postLoanMng_afterLoanList') && (
            <AsyncExport
              getSearchDataTotal={getSearchDataTotal}
              getSearchParams={() => {
                const params = formRef.current?.getFieldsFormatValue?.();
                const channelStoreParams = getChannelStoreRequestParams(access);
                return removeBlankFromObject(
                  filterProps({
                    ...channelStoreParams,
                    ...params,
                    classification,
                    secondaryClassification,
                  }),
                );
              }}
              trigger={
                <Button
                  type="primary"
                  onClick={(e) => {
                    if (!classification || !secondaryClassification) {
                      // 只要有一个不存在 就不能发请求
                      // 一级分类切换 由于联动 会清空二级分类
                      message.error('产品一级和二级分类必填');
                      e.stopPropagation();
                    }
                  }}
                >
                  导出
                </Button>
              }
              exportAsync={repayDetailExportAsync}
              taskCode={[
                ItaskCodeEnValueEnum.REPAYMENT_DETAIL_INSURANCE,
                ItaskCodeEnValueEnum.REPAYMENT_DETAIL_LEASE,
              ]}
            />
          ),
        ];
      }}
      columns={columns}
    />
  );
};

export default DetailList;
