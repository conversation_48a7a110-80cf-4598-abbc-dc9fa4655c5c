/*
 * @Author: elisa.zhao <EMAIL>
 * @Date: 2022-09-20 16:59:11
 * @LastEditors: elisa.zhao
 * @LastEditTime: 2024-10-16 10:23:33
 * @FilePath: /lala-finance-biz-web/src/pages/AfterLoan/repay-manage.tsx
 * @Description: repay-manage
 */
import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { CLASSIFICATION, REPURCHASE_STATUS_MAP, SECONDARY_CLASSIFICATION } from '@/enums';
import { getAllChannelNameEnum } from '@/services/enum';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import {
  disableFutureDate,
  getUuid,
  isCarInsuranceStoreUser,
  isChannelOrExternal,
  isChannelStoreUser,
} from '@/utils/utils';
import type { ActionType, ProColumns, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { Link, useAccess, useModel } from '@umijs/max';
import { Button, message } from 'antd';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { mapStatusZh } from '../../BusinessCashMng/const';
import { getChannelInfo, getProductNameEnum } from '../../CarInsurance/services';
import { LEVEL } from '../../CarInsurance/type';
import { getStoreAndApplyCityAll, orderRepayList, repayExportAsync } from '../services';
import type { RepayManageItem } from '../types';
import { secondaryClassificationEnCodeMap } from '../types';
import { getChannelStoreRequestParams, setFormValuesForChannelStore } from '../utils';

type Props = {
  classification: string;
  secondaryClassification?: keyof typeof SECONDARY_CLASSIFICATION;
};
const RepayManage: React.FC<Props> = (props) => {
  const { classification, secondaryClassification } = props;
  const access = useAccess();
  const actionRef = useRef<ActionType>();
  const formRef = useRef<ProFormInstance>();

  const [secondProduct, setSecondProduct] = useState(
    isChannelStoreUser(access) ? 'FINANCE_LEASE' : '',
  ); //  二级分类
  const [isCarInsurance, setIsCarInsurance] = useState(!isChannelStoreUser(access)); // 二级分类是否是车险 // 因为初始值默认给的车险 所有为true // 融租渠道帐户这里为false
  const [isRent, setIsRent] = useState(false);

  // 所有的
  const [productOptions, setProductOptions] = useState<{ value: string; label: string }[]>([]);
  // 不同二级分类下的
  const [currentProductOptions, setCurrentProductOptions] = useState<
    { value: string; label: string }[]
  >([]);

  const { initialState } = useModel('@@initialState');
  const { currentUser = {} } = initialState || {};
  const { channelCode, channelLevel } = currentUser as any;

  useEffect(() => {
    //融租渠道用户不需要产品搜索
    if (!isChannelStoreUser(access) && secondaryClassification) {
      // 获取 设置 所有的产品选项
      getProductNameEnum(secondaryClassification as keyof typeof SECONDARY_CLASSIFICATION).then(
        (res) => {
          setProductOptions(res);
        },
      );
    }
  }, [secondaryClassification, access]);

  useEffect(() => {
    setIsCarInsurance(secondaryClassification === 'CAR_INSURANCE');
    setIsRent(classification === 'FINANCE_LEASE');
    if (secondaryClassification) {
      setSecondProduct(secondaryClassification);
    }
  }, [secondaryClassification, classification]);

  // 当一级和二级分类发生变化 且都存在时 触发提交
  // 一级和二级发生变化，对应产品名称也会变化，所以要情况产品名称，让用户重新选择
  useEffect(() => {
    formRef.current?.setFieldValue('productCode', undefined);
    formRef.current?.setFieldValue('channelIds', undefined);
    if (classification && secondaryClassification) {
      formRef.current?.submit();
    }
  }, [classification, secondaryClassification]);

  // 设置二级分类对应的产品名称 二级分类不存在设置为空[]
  useEffect(() => {
    if (secondaryClassification && productOptions.length) {
      const twoCode =
        secondaryClassificationEnCodeMap[
          secondaryClassification as keyof typeof secondaryClassificationEnCodeMap
        ];
      setCurrentProductOptions(
        productOptions.filter((product) => product.value.substring(0, 4) === twoCode),
      );
    } else {
      setCurrentProductOptions([]);
    }
    actionRef?.current?.reset?.();
  }, [secondaryClassification, productOptions]);

  // 缓存
  const getStoreAndApplyCityAllMemo = useMemo(async () => {
    return await getStoreAndApplyCityAll();
  }, []);
  // const getAllChannelNameEnumMemo = useMemo(async () => {
  //   return await getAllChannelNameEnum();
  // }, []);

  // const getCarInsuranceChannelNameMemo = useMemo(async () => {}, []);

  const getChannelListSummarize = useMemo(async () => {
    if (secondaryClassification === 'CAR_INSURANCE') {
      return await getChannelInfo({ channelCode, channelLevel }).then((res) => {
        return res.map((item) => ({
          value: item.channelCode,
          label: item.channelName,
          title: item.channelName,
        }));
      });
    } else if (secondaryClassification === 'FINANCE_LEASE') {
      return await getAllChannelNameEnum();
    } else {
      return [];
    }
  }, [secondaryClassification, channelCode, channelLevel]);
  // console.log('access', access);

  // leaseStoreUser    融租门店用户 有一个固定的门店 access?.currentUser?.extSource?.storeId
  // leaseChannelUser  融租渠道用户 是固定的渠道
  // 对于以上用户需要初始化表单值
  useEffect(() => {
    setFormValuesForChannelStore(access, formRef);
  }, [access]);

  const columns: ProColumns<RepayManageItem>[] = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      fixed: 'left',
      width: '170px',
      render: (text, record) => {
        const toUrl = `/businessMng/postLoanMng/after-loan-detail?orderNo=${
          record.orderNo
        }&productCode=${record.productCode}&termDetail=${
          record.termDetail ? record.termDetail?.split('/')[0] : 0
        }`;
        const toCarInsuranceUrl = `/businessMng/postLoanMng/car-insurance/detail?orderNo=${text}`;
        let dom: React.ReactNode = '-';
        if (access.hasAccess('repayment_detail_postLoanMng_afterLoanList')) {
          dom = (
            <Link to={record.productCode?.substring(0, 4) === '0303' ? toCarInsuranceUrl : toUrl}>
              {record.orderNo}
            </Link>
          );
        } else {
          dom = (
            <Button disabled type="link">
              {record.orderNo}
            </Button>
          );
        }
        return dom;
      },
    },
    {
      title: '用户名称',
      dataIndex: 'accountName',
    },
    {
      title: '用户ID',
      dataIndex: 'accountNumber',
      search: false,
      hideInTable: isCarInsurance,
    },
    {
      title: '产品一级分类',
      dataIndex: 'classification',
      initialValue: 'FINANCE_LEASE',
      hideInTable: isCarInsurance,
      valueType: 'select',
      search: false,
      valueEnum: CLASSIFICATION,
      // fieldProps: {
      //   allowClear: false,
      //   onChange(value: string) {
      //     if (value === 'SMALL_LOAN') {
      //       // 场景 一级分类是 非小额贷款时 二级分类可以清空
      //       //     一级分类的 小额贷款时 二级分类的不可以清空 是必填的
      //       // setFieldValue 不会触发组件重新render
      //       setIsAllowClear(false);
      //       formRef.current?.setFieldValue('secondaryClassification', 'CAR_INSURANCE');
      //     } else {
      //       setIsAllowClear(true);
      //     }
      //   },
      //   disabled: isChannelStoreUser(access),
      // },
    },
    {
      title: '产品二级分类',
      dataIndex: 'secondaryClassification',
      valueType: 'select',
      initialValue: isChannelStoreUser(access) ? 'FINANCE_LEASE' : 'CAR_INSURANCE', //  融租渠道用户筛选小圆车融
      valueEnum: SECONDARY_CLASSIFICATION,
      hideInTable: true,
      search: false,
      // fieldProps: {
      //   onChange: (value: string) => {
      //     setIsCarInsurance(value === 'CAR_INSURANCE');
      //     setSecondProduct(value);
      //     setIsRent(value === 'FINANCE_LEASE');
      //     formRef?.current?.submit();
      //   },
      //   disabled: isChannelStoreUser(access),
      // },
    },
    {
      title: '产品名称',
      dataIndex: 'productCode',
      // valueEnum: productNameList,
      search: !isChannelStoreUser(access) as any, //  渠道门店帐号不需展示产品名称筛选
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        options: currentProductOptions,
      },
      hideInTable: true,
    },
    {
      title: '投保主体',
      dataIndex: 'insuranceSubject',
      search: false,
      hideInTable: !isCarInsurance,
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      search: false,
    },
    {
      title: '退车状态',
      dataIndex: 'leaseRefund',
      search: false,
      hideInTable: !isRent,
      render(_: any, record: any) {
        return record?.leaseRefund ? '已退车' : '未退车';
      },
    },
    {
      title: '还款方式',
      dataIndex: 'repayMode',
      search: false,
      valueEnum: {
        1: '一次本息',
        2: '等额本息',
      },
    },
    {
      title: '还款期限',
      dataIndex: 'repayTerm',
      valueEnum: {
        1: '固定还款日',
      },
      search: false,
    },
    {
      title: '借款利率',
      dataIndex: 'annualInterestRate',
      search: false,
      render: (_, row) => {
        // console.log(annualInterestRate);
        return row?.annualInterestRate
          ? `${new BigNumber(row?.annualInterestRate).times(100)}%`
          : '-';
      },
    },
    {
      title: '资金渠道',
      dataIndex: 'loanChannelCode',
      search: false,
      hideInTable: isCarInsurance,
    },
    {
      title: '渠道类型',
      dataIndex: 'leaseChannelType',
      search: false,
      hideInTable: isCarInsurance,
    },
    {
      title: '渠道名称',
      dataIndex: 'leaseChannelName',
      search: false,
      // hideInTable: isCarInsurance,
    },
    {
      title: '门店',
      dataIndex: 'leaseStoreName',
      search: false,
      hideInTable: secondProduct !== 'FINANCE_LEASE',
    },
    {
      title: '放款日期',
      dataIndex: 'lendingTime',
      search: false,
    },
    {
      title: '创建日期',
      dataIndex: 'createTime',
      valueType: 'dateRange',
      fieldProps: {
        ranges: {
          最近半年: [dayjs().subtract(6, 'month'), dayjs()],
          最近一年: [dayjs().subtract(1, 'year'), dayjs()],
          最近两年: [dayjs().subtract(2, 'year'), dayjs()],
        },
        disabledDate: disableFutureDate,
      },
      initialValue: [dayjs().subtract(1, 'year'), dayjs()],
      search: {
        transform: (value: any) => {
          return {
            startCreatedAt: `${value[0]}`,
            endCreatedAt: `${value[1]}`,
          };
        },
      },
      render: (_, record) => {
        return record.createTime || '-';
      },
    },
    {
      title: '应结清日期',
      dataIndex: 'lastRepayTime',
      search: false,
    },
    {
      title: '实际结清日期',
      dataIndex: 'lastClearDate',
      valueType: 'dateRange',
      fieldProps: {
        ranges: {
          待确定: [dayjs('2220-01-01'), dayjs('2220-01-01')],
        },
        format: (value: dayjs.Dayjs) => {
          if (value.format('YYYY-MM-DD') === '2220-01-01') {
            return `待确定`;
          }
          return value.format('YYYY-MM-DD');
        },
      },
      search: {
        transform: (value: any) => {
          return {
            clearStartTime: value[0] === '待确定' ? '2220-01-01' : `${value[0]}`,
            clearEndTime: value[1] === '待确定' ? '2220-01-01' : `${value[1]}`,
          };
        },
      },
      render: (_, record) => {
        return record.lastClearDate ? record.lastClearDate : '待确定';
      },
    },
    {
      title: '应还总额',
      dataIndex: 'amountDue',
      search: false,
    },
    {
      title: '应还本金',
      dataIndex: 'principal',
      search: false,
    },
    {
      title: '应还利息',
      dataIndex: 'interest',
      search: false,
    },
    {
      title: '应还逾期罚息',
      dataIndex: 'overdueInterest',
      search: false,
    },
    {
      title: '应还逾期滞纳金',
      dataIndex: 'overdueLatePaymentFee',
      search: false,
    },
    {
      title: '应还结清违约金',
      dataIndex: 'advanceSettleLiquidatedDamages',
      search: false,
    },
    {
      title: '应还月供额',
      dataIndex: 'firstRepayAmountSum',
      search: false,
      hideInTable: isCarInsurance,
    },
    {
      title: '已还总额',
      dataIndex: 'amountPaid',
      search: false,
    },
    {
      title: '已还本金',
      dataIndex: 'principalPaid',
      search: false,
    },
    {
      title: '已还利息',
      dataIndex: 'interestPaid',
      search: false,
    },
    {
      title: '已还逾期罚息',
      dataIndex: 'overdueInterestPaid',
      search: false,
    },
    {
      title: '已还逾期滞纳金',
      dataIndex: 'overdueLatePaymentFeePaid',
      search: false,
    },
    {
      title: '已还结清违约金',
      dataIndex: 'advanceSettleLiquidatedDamagesPaid',
      search: false,
    },
    {
      title: '剩余未还',
      dataIndex: 'remainingAmountDue',
      search: false,
    },
    {
      title: '剩余本金',
      dataIndex: 'remainingPrincipal',
      search: false,
    },
    {
      title: '剩余利息',
      dataIndex: 'remainingInterest',
      search: false,
    },
    {
      title: '剩余逾期罚息',
      dataIndex: 'remainingOverdueInterest',
      search: false,
    },
    {
      title: '剩余逾期滞纳金',
      dataIndex: 'remainingOverdueLatePaymentFee',
      search: false,
    },
    {
      title: '催收金额',
      dataIndex: 'recallAmount',
      search: false,
      hideInTable: isCarInsurance,
    },
    {
      title: '减免金额',
      dataIndex: 'remissionAmount',
      search: false,
    },
    {
      title: '退保盈余',
      dataIndex: 'cancelProfitAmount',
      search: false,
      hideInTable: !isCarInsurance,
    },
    {
      title: '逾期天数',
      dataIndex: 'overDueDay',
      search: false,
    },
    {
      dataIndex: 'licensePlateNo',
      title: '车牌号',
      search: isCarInsurance as any,
      hideInTable: true,
    },
    {
      dataIndex: 'vin',
      title: '车架号',
      search: isCarInsurance,
      hideInTable: true,
    },
    {
      title: '还款状态',
      dataIndex: 'status',
      hideInTable: isCarInsurance,
      valueEnum: mapStatusZh,
    },
    {
      title: '逾期代偿回购',
      dataIndex: 'repurchaseStatus',
      search: false,
      hideInTable: !isRent || isChannelStoreUser(access),
      valueEnum: REPURCHASE_STATUS_MAP,
    },
    {
      title: '回购金额',
      dataIndex: 'buybackAmount',
      search: false,
      hideInTable: isChannelOrExternal(access),
      render: (_, row) => {
        // 未回购及不涉及回购的情况，展示‘-’
        return [0, 1].includes(row?.repurchaseStatus as number) ? '-' : row?.buybackAmount ?? '-';
      },
    },
    {
      title: '首次回购时间',
      dataIndex: 'buybackFirstTime',
      search: false,
      hideInTable: isChannelOrExternal(access),
      render: (_, row) => {
        // 未回购及不涉及回购的情况，展示‘-’
        return [0, 1].includes(row?.repurchaseStatus as number)
          ? '-'
          : row?.buybackFirstTime ?? '-';
      },
    },
    {
      title: '回购完成时间',
      dataIndex: 'buybackSucceedTime',
      search: false,
      hideInTable: isChannelOrExternal(access),
      render: (_, row) => {
        // 未回购及不涉及回购的情况，展示‘-’
        return [0, 1].includes(row?.repurchaseStatus as number)
          ? '-'
          : row?.buybackSucceedTime ?? '-';
      },
    },
    {
      title: '渠道名称',
      dataIndex: 'channelIds',
      params: { key: getUuid() },
      valueType: 'select',
      //车险或者融租才有渠道
      search:
        secondProduct === 'FINANCE_LEASE' || secondProduct === 'CAR_INSURANCE' ? undefined : false, //车险和车融展示渠道
      request: async () => getChannelListSummarize,
      hideInTable: true,
      //二级车险渠道只有自己，禁用渠道同时，选中自己
      initialValue:
        (isCarInsuranceStoreUser(access) && channelLevel === LEVEL.SECOND_CHANNEL) ||
        (isChannelStoreUser(access) && !!access.currentUser?.channelCode)
          ? [channelCode]
          : undefined,
      fieldProps: {
        showSearch: true,
        mode: 'multiple',
        showArrow: true,
        disabled:
          (isChannelStoreUser(access) && !!access.currentUser?.channelCode) ||
          (isCarInsuranceStoreUser(access) && channelLevel === LEVEL.SECOND_CHANNEL),
        optionFilterProp: 'label',
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: '门店',
      dataIndex: 'storeIds',
      params: { key: 'storeIds_repay_manage' },
      search: secondProduct !== 'FINANCE_LEASE' ? false : undefined,
      hideInTable: true,
      request: () => {
        return getStoreAndApplyCityAllMemo.then((res) => {
          return (
            res?.data?.storeList?.map((item: { storeName: string; id: string }) => {
              return { value: item.id?.toString(), label: item.storeName };
            }) || []
          );
        });
      },
      fieldProps: {
        showSearch: true,
        optionFilterProp: 'label',
        mode: 'multiple',
        // 如果是车险渠道 二级 用户只能查看自己渠道的
        disabled: isChannelStoreUser(access) && !!access.currentUser?.extSource?.storeId,
        showArrow: true,
        filterOption: (input: string, option: { label: string }) =>
          option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0,
      },
    },
    {
      title: 'repayPlanNo',
      dataIndex: 'repayPlanNo',
      search: false,
      hideInTable: true,
    },
    {
      title: '期数',
      dataIndex: 'termDetail',
      search: false,
      hideInTable: true,
    },
    {
      title: '借款期限',
      dataIndex: 'totalTerm',
      search: false,
      hideInTable: true,
    },
  ];

  async function getSearchDataTotal() {
    const searchParams = removeBlankFromObject(
      filterProps(formRef.current?.getFieldsFormatValue?.()),
    );
    const data = await orderRepayList({
      ...searchParams,
      classification,
      secondaryClassification,
    });
    return data?.total;
  }

  return (
    <div>
      <ProTable
        actionRef={actionRef}
        formRef={formRef}
        rowKey="repayNo"
        search={{ labelWidth: 100, defaultCollapsed: false }}
        scroll={{ x: 'max-content' }}
        onSubmit={() => {
          if (!classification || !secondaryClassification) {
            // 只要有一个不存在 就不能发请求
            // 一级分类切换 由于联动 会清空二级分类
            message.error('产品一级和二级分类必填');
          }
        }}
        request={(params) => {
          if (!classification || !secondaryClassification) {
            // 只要有一个不存在 就不能发请求
            // 一级分类切换 由于联动 会清空二级分类
            return {} as any;
          }
          const channelStoreParams = getChannelStoreRequestParams(access);
          return orderRepayList(
            removeBlankFromObject(
              filterProps({
                ...channelStoreParams,
                ...params,
                classification,
                secondaryClassification,
              }),
            ),
          ).then((res) => {
            const { endCreatedAt, startCreatedAt } = res?.queryParam || {};
            if (endCreatedAt && startCreatedAt) {
              // 回显表单的注册时间
              formRef?.current?.setFieldsValue({
                createTime: [dayjs(startCreatedAt), dayjs(endCreatedAt)],
              });
            } else {
              // 清空表单的注册时间
              formRef?.current?.setFieldsValue({
                createTime: undefined,
              });
            }
            return res;
          });
        }}
        toolBarRender={() => {
          return [
            access.hasAccess('bill_cms_repay_list_export_postLoanMng_afterLoanList') && (
              <AsyncExport
                getSearchDataTotal={getSearchDataTotal}
                getSearchParams={() => {
                  const params = formRef.current?.getFieldsFormatValue?.();
                  const channelStoreParams = getChannelStoreRequestParams(access);
                  return removeBlankFromObject(
                    filterProps({
                      ...channelStoreParams,
                      ...params,
                      classification,
                      secondaryClassification,
                    }),
                  );
                }}
                trigger={
                  <Button
                    type="primary"
                    onClick={(e) => {
                      if (!classification || !secondaryClassification) {
                        // 只要有一个不存在 就不能发请求
                        // 一级分类切换 由于联动 会清空二级分类
                        message.error('产品一级和二级分类必填');
                        e.stopPropagation();
                      }
                    }}
                  >
                    导出
                  </Button>
                }
                // /bizadmin/repayment/order/repay/exportAsync
                exportAsync={repayExportAsync}
                taskCode={[
                  ItaskCodeEnValueEnum.REPAYMENT_LOAN_MGR,
                  ItaskCodeEnValueEnum.REPAYMENT_FINANCE_MGR,
                  ItaskCodeEnValueEnum.REPAYMENT_INSURANCE_MGR,
                ]}
              />
            ),
          ];
        }}
        dateFormatter="string"
        columns={columns}
      />
    </div>
  );
};

export default RepayManage;
