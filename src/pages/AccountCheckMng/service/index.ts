import { bizAdminHeader } from '@/utils/constant';
import { request } from '@umijs/max';

// 获取查账记账列表
export const getAccountCheckList = (params: any) => {
  return request('/bizadmin/bill/queryBillFlow', {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
};

// 获取查账记账详情
export const getAccountCheckDetail = (flowNo: string) => {
  return request(`/biz/account-check/detail/${flowNo}`, {
    method: 'GET',
    headers: bizAdminHeader,
  });
};

// 查账记账详情使用记录
export const getAccountCheckDetailUseRecord = (params: any) => {
  return request(`/bizadmin/bill/queryBillFlowUseRecord`, {
    method: 'POST',
    data: params,
    headers: bizAdminHeader,
  });
};

// 查账记账导出
export async function accountCheckExport(params: any) {
  return request(`/bizadmin/bill/exportBillFlow`, {
    headers: bizAdminHeader,
    method: 'POST',
    data: params,
    ifTrimParams: true,
  });
}
