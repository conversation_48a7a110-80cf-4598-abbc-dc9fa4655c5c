/**
 * @Author: alan771.tu
 * @Date: 2025-07-23 10:00:00
 * @LastEditors: alan771.tu
 * @LastEditTime: 2025-07-23 10:00:00
 * @FilePath: lala-finance-biz-web/src/pages/AccountCheckMng/modules/CarInsuranceAccountList.tsx
 * @Description: CarInsuranceAccountList.tsx
 */
import { getSortOrder } from '@/utils/tools';
import type { ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { history, useModel } from '@umijs/max';
import type { Key } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import React, { memo, useEffect, useMemo, useRef, useState } from 'react';

import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import { useAccess } from '@umijs/max';
import { Button, message } from 'antd';
import BigNumber from 'bignumber.js';
import { accountCheckExport } from '../service';

type Props = {};
const CarInsuranceAccountList: React.FC<Props> = () => {
  const formRef = useRef<ProFormInstance>();
  const { selectedData, dispatch } = useModel('AccountCheckMng.shared');
  const access = useAccess();
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>(selectedData.selectedRowKeys || []);
  // 无论分页怎么变化 只要含有key 就会被勾选
  const [selectedRows, setSelectedRows] = useState<Record<string, any>[]>(
    selectedData.selectedRows || [],
  );
  // const { initialState = {} }: any = useModel<any>('@@initialState');
  // const { currentUser = {} } = initialState;
  // const { channelCode, channelLevel } = currentUser;
  // const [currentPage, setCurrentPage] = useState(1);
  // const actionRef = useRef<ActionType>();

  // 监听全局数据
  useEffect(() => {
    setSelectedRowKeys(selectedData.selectedRowKeys || []);
    setSelectedRows(selectedData.selectedRows || []);
  }, [selectedData]);

  // 如果当前列表取消选择，则重置全局数据
  useEffect(() => {
    if (!selectedRowKeys.length) {
      dispatch({
        type: 'reset-account-check-mng',
      });
    }
  }, [selectedRowKeys, dispatch]);

  function caculate(rows: any) {
    const amount = rows.reduce((pre: any, cur: any) => {
      return new BigNumber(cur.unusedAmount || 0).plus(pre);
    }, 0);
    const applyAmount = Number(new BigNumber(amount));
    return applyAmount;
  }

  // 已选金额
  const selectAmounts = useMemo(() => {
    return caculate(selectedRows);
  }, [selectedRows]);

  // 取消选择
  function cancelSelect() {
    setSelectedRowKeys([]);
    setSelectedRows([]);
    // 通知账单管理页面取消选择
    dispatch({
      type: 'reset-account-check-mng',
    });
  }

  function getSelectedAlertRender() {
    if (!selectedRows.length) {
      return null;
    }
    return (
      <div
        style={{
          padding: '0 24px',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <div>
          已选 <span style={{ color: '#1890ff' }}>{selectedRows.length}</span> 条流水,
          合计可用金额：
          <span style={{ color: '#1890ff' }}>{selectAmounts} 元</span>
        </div>
        <Button
          type="link"
          style={{ color: '#f42f2c' }}
          onClick={() => {
            cancelSelect();
          }}
        >
          取消选择
        </Button>
      </div>
    );
  }

  const orderSort = [
    'payAccount',
    'payAccountName',
    'transactionDate',
    'amount',
    'remark',
    'flowNo',
    'payType',
  ];

  const columns = [
    {
      title: '交易时间',
      dataIndex: 'transactionTime',
      valueType: 'dateTime',
      search: false,
    },
    {
      title: '收/付款帐号',
      dataIndex: 'payAccount',
      hideInTable: true,
      order: getSortOrder('payAccount', orderSort),
    },
    {
      title: '收/付款帐户',
      dataIndex: 'payAccountName',
      hideInTable: true,
      order: getSortOrder('payAccountName', orderSort),
    },
    {
      title: '交易日期',
      dataIndex: 'transactionDate',
      valueType: 'dateTimeRange',
      // 默认一年
      initialValue: [dayjs().subtract(1, 'year'), dayjs()],
      fieldProps: {
        allowClear: false,
      },
      order: getSortOrder('transactionDate', orderSort),
      search: {
        transform: (value: any) => {
          return {
            transactionDateStart: dayjs(value[0]).format('YYYY-MM-DD 00:00:00'),
            transactionDateEnd: dayjs(value[1]).format('YYYY-MM-DD 23:59:59'),
          };
        },
      },
      hideInTable: true,
    },
    {
      title: '流水号',
      dataIndex: 'flowNo',
      order: getSortOrder('flowNo', orderSort),
    },
    {
      title: '收/付款',
      dataIndex: 'payType',
      valueEnum: {
        '1': '收款',
        '2': '付款',
      },
      order: getSortOrder('payType', orderSort),
    },
    {
      title: '收/付信息',
      dataIndex: 'payInfo',
      search: false,
    },
    {
      title: '收/付金额',
      dataIndex: 'amount',
      valueType: 'money',
      order: getSortOrder('amount', orderSort),
    },
    {
      title: '备注',
      dataIndex: 'remark',
    },
    {
      title: '已用金额',
      dataIndex: 'usedAmount',
      valueType: 'money',
      search: false,
    },
    {
      title: '未用金额',
      dataIndex: 'unusedAmount',
      valueType: 'money',
      search: false,
    },
    // {
    //   title: '状态',
    //   dataIndex: 'status',
    //   valueEnum: {
    //     '0': '未入账',
    //     '1': '已入账',
    //     '2': '部分入账',
    //   },
    //   search: false,
    //   hideInTable: true,
    // },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right' as const,
      render: (text, record) => {
        return (
          <Button
            type="link"
            onClick={() => {
              history.push(`/businessMng/account-check-detail?flowNo=${record.flowNo}`);
            }}
          >
            详情
          </Button>
        );
      },
    },
  ];

  const mockData = Array.from({ length: 100 }, (_, index) => ({
    id: index,
    billNo: `**********${index}`,
    flowNo: `**********${index}`,
    transactionTime: dayjs().subtract(index, 'day').format('YYYY-MM-DD HH:mm:ss'),
    payType: Math.ceil(Math.random() * 2),
    payInfo: '收/付信息',
    amount: Number((Math.random() * 100).toFixed(2)),
    remark: '备注',
    usedAmount: Number((Math.random() * 100).toFixed(2)),
    unusedAmount: Number((Math.random() * 100).toFixed(2)),
    status: Math.floor(Math.random() * 3),
  }));

  // 线下还款
  function offlineRepay() {
    if (!selectedRows.length) {
      message.warning('请先选择流水号');
      return;
    }

    if (selectAmounts <= 0) {
      message.warning('请选择有可用金额的流水号');
      return;
    }

    // 派发全局数据
    dispatch({
      type: 'account-check-mng',
      payload: {
        selectedRowKeys,
        selectedRows,
      },
    });

    // 跳转到账单管理页面-并回显已选择的流水号信息（仅限产品二级分类为车险分期时）
    history.push(`/businessMng/bill-manager`);
  }

  return (
    <ProTable
      search={{
        defaultCollapsed: false,
        labelWidth: 'auto',
      }}
      columns={columns}
      request={async (values) => {
        console.log('values', values);
        return {
          data: mockData,
          success: true,
        };
      }}
      rowKey="id"
      scroll={{ x: 'max-content' }}
      tableExtraRender={() => {
        return getSelectedAlertRender();
      }}
      rowSelection={{
        selectedRowKeys: selectedRowKeys,
        onChange: (rowKeys, rowItems) => {
          setSelectedRowKeys(rowKeys);
          setSelectedRows(rowItems);
        },
      }}
      tableAlertOptionRender={false}
      tableAlertRender={false}
      formRef={formRef}
      // pagination={{
      //   pageSizeOptions: [10, 20, 50, 100],
      //   showSizeChanger: true,
      //   onShowSizeChange: (current_Page, _) => {
      //     setCurrentPage(current_Page);
      //   },
      // }}
      toolBarRender={() => {
        return [
          <Button
            key="account-list-offline-repay"
            type="primary"
            onClick={() => {
              offlineRepay();
            }}
          >
            线下还款
          </Button>,
          <AsyncExport
            key="account-check-export"
            getSearchDataTotal={async () => {
              return Promise.resolve(100);
              //  const values = formRef.current?.getFieldsFormatValue?.();
              //  const { billNo, termList } = values;
              //  const params: IbillListParams = {
              //    ...values,
              //    billNoList: billNo ? [billNo] : undefined,
              //    termList: termList ? [termList] : undefined,
              //    dimension: IdimensionEnCode.TERM_BILL,
              //    current: 1,
              //    pageSize: 1,
              //    secondaryClassification: '0303',
              //  };
              //  const data = await billList(removeBlankFromObject(filterProps(params)));
              //  return data?.total;
            }}
            getSearchParams={() => {
              //  const values = formRef.current?.getFieldsFormatValue?.();
              //  return removeBlankFromObject(filterProps(params));
            }}
            getSelectedParams={() => {
              //  const values = formRef.current?.getFieldsFormatValue?.();
            }}
            getSelectedTotal={() => {
              return selectedRows.length;
            }}
            exportAsync={accountCheckExport}
            taskCode={[ItaskCodeEnValueEnum.ACCOUNT_CHECK_BILL_REPAY_DETAIL_EXPORT]}
            trigger={<Button type="primary">导出入帐明细</Button>}
          />,
          <AsyncExport
            key="account-check-export-flow"
            getSearchDataTotal={async () => {
              return Promise.resolve(100);
              //  const values = formRef.current?.getFieldsFormatValue?.();
              //  const { billNo, termList } = values;
              //  const params: IbillListParams = {
              //    ...values,
              //    billNoList: billNo ? [billNo] : undefined,
              //    termList: termList ? [termList] : undefined,
              //    dimension: IdimensionEnCode.TERM_BILL,
              //    current: 1,
              //    pageSize: 1,
              //    secondaryClassification: '0303',
              //  };
              //  const data = await billList(removeBlankFromObject(filterProps(params)));
              //  return data?.total;
            }}
            getSearchParams={() => {
              //  const values = formRef.current?.getFieldsFormatValue?.();
              //  return removeBlankFromObject(filterProps(params));
            }}
            getSelectedParams={() => {
              //  const values = formRef.current?.getFieldsFormatValue?.();
            }}
            getSelectedTotal={() => {
              return selectedRows.length;
            }}
            exportAsync={accountCheckExport}
            taskCode={[ItaskCodeEnValueEnum.ACCOUNT_CHECK_BILL_REPAY_FLOW_EXPORT]}
            trigger={<Button type="primary">导出流水</Button>}
          />,
        ];
      }}
    />
  );
};

export default memo(CarInsuranceAccountList);
