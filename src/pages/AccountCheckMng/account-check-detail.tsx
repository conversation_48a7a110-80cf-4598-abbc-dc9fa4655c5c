/*
 * @Author: your name
 * @Date: 2021-01-11 14:22:16
 * @LastEditTime: 2023-03-13 16:44:53
 * @LastEditors: elisa.zhao <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/pages/AfterLoan/after-loan-detail.tsx
 */
import HeaderTab from '@/components/HeaderTab/index';
import { ProDescriptions, ProTable } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { history } from '@umijs/max';
import { Card, Divider } from 'antd';
import React from 'react';

// import { getAccountCheckDetail, getAccountCheckDetailUseRecord } from './service';

const AccountCheckDetail: React.FC<any> = () => {
  const { flowNo } = history.location.query as any;
  console.log('flowNo', flowNo);

  // const { data } = useRequest(() => getAccountCheckDetail(flowNo), {
  //   manual: true,
  //   ready: !!flowNo,
  // });

  const mockData = {
    flowNo: '**********',
    transactionDate: '2021-01-01',
    transactionTime: '2021-01-01 12:00:00',
    payAccount: '**********',
    payAccountName: '张三',
    payAmount: 10000,
    payType: '0',
    payBank: '中国银行',
    remark: '备注',
    usedAmount: 10000,
    unusedAmount: 10000,
    status: '0',
  };

  const descriptionColumns = [
    {
      title: '流水号',
      dataIndex: 'flowNo',
      valueType: 'text',
    },
    {
      title: '交易日',
      dataIndex: 'transactionDate',
      valueType: 'date',
    },
    {
      title: '交易时间',
      dataIndex: 'transactionTime',
      valueType: 'dateTime',
    },
    {
      title: '收/付款',
      dataIndex: 'payType',
      valueEnum: {
        '0': '收款',
        '1': '付款',
      },
      valueType: 'select',
    },
    {
      title: '收/付款账户',
      dataIndex: 'payAccountName',
      valueType: 'text',
    },
    {
      title: '收/付款账号',
      dataIndex: 'payAccount',
      valueType: 'text',
    },
    {
      title: '收/付款银行',
      dataIndex: 'payBank',
      valueType: 'text',
    },
    {
      title: '收/付款金额',
      dataIndex: 'payAmount',
      valueType: 'money',
    },
    {
      title: '备注',
      dataIndex: 'remark',
      valueType: 'text',
    },
    {
      title: '已用金额',
      dataIndex: 'usedAmount',
      valueType: 'money',
    },
    {
      title: '未用金额',
      dataIndex: 'unusedAmount',
      valueType: 'money',
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueType: 'select',
      valueEnum: {
        '0': '待处理',
        '1': '已处理',
      },
    },
  ];

  const tableColumns = [
    {
      title: '类型',
      dataIndex: 'type',
      valueType: 'select',
      valueEnum: {
        '1': '自动使用',
        '2': '入账场景使用',
      },
    },
    {
      title: '使用场景',
      dataIndex: 'scene',
      valueType: 'select',
      valueEnum: {
        '1': '还款/结清',
        '2': '退保',
        '3': '减保',
        '4': '加保',
        '5': '线上首付',
        '6': '线下首付',
        '7': '线上金融放款',
        '8': '线下金融放款',
        '9': '线上委托投保',
        '10': '线下委托投保',
        '11': '未承保退费',
        '12': '错误打款',
      },
    },
    {
      title: '使用金额',
      dataIndex: 'useAmount',
      valueType: 'money',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
    },
    {
      title: '创建人',
      dataIndex: 'createUser',
      valueType: 'text',
    },
    {
      title: '使用时间',
      dataIndex: 'useTime',
      valueType: 'dateTime',
    },
    {
      title: '审核记录',
      dataIndex: 'auditRecord',
      valueType: 'text',
    },
    {
      title: '使用状态',
      dataIndex: 'useStatus',
      valueType: 'select',
      valueEnum: {
        '0': '待处理',
        '1': '已处理',
      },
    },
  ];

  const tableData = Array.from({ length: 1000 }, (_, index) => ({
    id: index + 1,
    type: Math.ceil(Math.random() * 2),
    scene: Math.ceil(Math.random() * 12),
    useAmount: 10000,
    createTime: '2021-01-01 12:00:00',
    createUser: '张三',
    useTime: '2021-01-01 12:00:00',
    auditRecord: '审核记录',
    useStatus: Math.floor(Math.random() * 2),
  }));

  // console.log('data', data);
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <Card>
          {/* 基本信息 */}
          <ProDescriptions
            column={4}
            title="基本信息"
            columns={descriptionColumns}
            dataSource={mockData}
          />
          <Divider />
          {/* 使用记录 */}
          <div className="check-record-list">
            <h3>使用记录</h3>
            <ProTable
              rowKey="id"
              columns={tableColumns}
              toolBarRender={false}
              search={false}
              options={false}
              request={async (params) => {
                return {
                  data: tableData,
                  success: true,
                  total: tableData.length,
                };
              }}
            />
          </div>
        </Card>
      </PageContainer>
    </>
  );
};

export default AccountCheckDetail;
