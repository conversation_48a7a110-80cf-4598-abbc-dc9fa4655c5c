/*
 * @Author: alan7.tu <EMAIL>
 * @Date: 2022-09-20 16:06:52
 * @LastEditors: alan7.tu <EMAIL>
 * @LastEditTime: 2025-07-17 17:38:52
 * @FilePath: /lala-finance-biz-web/src/pages/AccountCheckMng/index.tsx
 */
import HeaderTab from '@/components/HeaderTab/index';
import { isCarInsuranceStoreUser, isChannelStoreUser } from '@/utils/utils';
import { ProFormSelect } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { useAccess } from '@umijs/max';
import React, { useState } from 'react';
import { KeepAlive } from 'react-activation';
import CarInsuranceAccountList from './modules/CarInsuranceAccountList'; // 车险查账入账

import type { PRODUCT_CLASSIFICATION_BILL_MAP } from './types';
import { PRODUCT_CLASSIFICATION_BILL } from './types';

const AccountCheckMng: React.FC<any> = () => {
  const [classification, setClassification] = useState<PRODUCT_CLASSIFICATION_BILL_MAP>(
    'CAR_INSURANCE',
  ); // 一级分类
  const [classificationOrder, setClassificationOrder] = useState<string>('123'); // 账单纬度分类，默认展示【易人行小贷银行帐户】

  const access = useAccess();

  const mockSecondaryClassification = {
    CAR_INSURANCE: {
      '123': '易人行小贷银行帐户',
      '456': '易人行小贷微信商户号',
      '789': '易人行小贷支付宝商户号',
      '101': '易人行小贷通联商户号',
      '102': '货满满银行帐户',
      '103': '货满满微信商户号',
      '104': '货满满支付宝商户号',
      '105': '易立信银行帐户',
      '106': '易立信微信商户号',
      '107': '易立信支付宝商户号',
      '108': '惠圆银行帐户',
      '109': '惠圆微信商户号',
      '110': '圆圆不断银行帐户',
      '111': '圆圆不断微信商户号',
    },
  };

  function getExtra() {
    return (
      <div style={{ display: 'flex', gap: 20, alignItems: 'center', flex: 1 }}>
        <ProFormSelect
          formItemProps={{
            style: { margin: 0 },
          }}
          fieldProps={{
            value: classification,
            onChange: (val) => {
              setClassification(val);
              setClassificationOrder('123'); // 默认展示【易人行小贷银行帐户】
            },
            allowClear: false,
            disabled: isChannelStoreUser(access) || isCarInsuranceStoreUser(access),
          }}
          label="产品二级分类"
          valueEnum={PRODUCT_CLASSIFICATION_BILL}
        />
        <ProFormSelect
          formItemProps={{
            style: { margin: 0 },
          }}
          fieldProps={{
            value: classificationOrder,
            onChange: (val) => {
              setClassificationOrder(val);
            },
            allowClear: false,
          }}
          label="帐户"
          valueEnum={mockSecondaryClassification[classification]}
        />
      </div>
    );
  }
  console.log(
    'classification',
    classification,
    'secondaryClassifcation二级分类',
    mockSecondaryClassification,
  );
  return (
    <>
      <PageContainer extra={getExtra()}>
        <CarInsuranceAccountList />
      </PageContainer>
    </>
  );
};

export default () => (
  <>
    <HeaderTab />
    <KeepAlive name="businessMng/account-check-manager">
      <AccountCheckMng />
    </KeepAlive>
  </>
);
