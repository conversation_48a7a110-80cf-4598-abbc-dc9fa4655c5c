import BigNumber from 'bignumber.js';
import { useReducer } from 'react';

type State = {
  selectedRowKeys: string[];
  selectedRows: any[];
  selectedAmount: string | number;
  hasSelected: boolean;
};

type AccountAction = {
  type: 'account-check-mng' | 'reset-account-check-mng';
  payload?: {
    selectedRowKeys: string[];
    selectedRows: any[];
  };
};

const reducer = (state: State, action: AccountAction) => {
  switch (action.type) {
    case 'account-check-mng':
      return {
        ...state,
        selectedRowKeys: action.payload?.selectedRowKeys || [],
        selectedRows: action.payload?.selectedRows || [],
        selectedAmount: caculate(action.payload?.selectedRows || []),
        hasSelected: (action.payload?.selectedRows?.length || 0) > 0,
      };
    case 'reset-account-check-mng':
      return {
        ...state,
        selectedRowKeys: [],
        selectedRows: [],
        selectedAmount: '0.00',
        hasSelected: false,
      };
    default:
      throw new Error('Invalid action');
  }
};

function caculate(rows: any[]) {
  return new BigNumber(rows.reduce((pre, cur) => pre + cur.unusedAmount, 0)).toFixed(2);
}

export default function useAccountCheckMng() {
  const [selectedData, dispatch] = useReducer(reducer, {
    selectedRowKeys: [],
    selectedRows: [],
    selectedAmount: '0.00',
    hasSelected: false,
  });
  console.log('selectedData', selectedData);

  return { selectedData, dispatch };
}
