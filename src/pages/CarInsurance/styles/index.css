

.car-insurance-edit .ant-pro-page-container-children-content{
    margin: 0;
}

.car-edit{
  display: flex;
  justify-content: space-around;
  gap: 20px;
  height: 60vh;
  /* scoll 一直有滚动条 auto 超出才有滚动条 */
  overflow-y: auto;
}

.car-main .ant-tabs-ink-bar{
    display: none;
}

/* .car-main .ant-tabs-nav{
    max-height: 600px;
} */
.car-main .ant-tabs-tab{
    margin: 0 !important;
    padding: 8px 2px !important;
    text-align: start !important;
    width: 240px !important;
}
.car-main .ant-tabs-tab-active{
    background-color: #bae0ff;
}
.car-main .ant-tabs-tab-btn{
    color: #262626 !important;
}

.car-main .ant-descriptions-item-label::after{
    display: none;
}
.car-edit .ant-form-item{
    margin-bottom: 12px;
}

.car-carousel  button {
  height: 16px !important;
  width: 16px !important;
  border-radius: 50% !important;
  background-color: #DADADA !important;
  opacity: 1 !important;
}

.car-carousel .slick-active button {
  background-color: #005BFD !important;
}

.car-carousel .slick-active{
  width: 16px !important;
}


