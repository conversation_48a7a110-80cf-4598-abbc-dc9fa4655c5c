import AsyncExport from '@/components/AsyncExport';
import { ItaskCodeEnValueEnum } from '@/components/AsyncExport/types';
import HeaderTab from '@/components/HeaderTab';
import LoadingButton from '@/components/LoadingButton';
import { filterProps, removeBlankFromObject } from '@/utils/tools';
import type { ActionType, ProFormInstance } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useAccess, useModel } from '@umijs/max';
import { BlobWriter, ZipWriter } from '@zip.js/zip.js';
import { Button, Form, message, Modal, Select, Tag } from 'antd';
import React, { memo, useEffect, useRef, useState } from 'react';
import getColumns from './columns';
import {
  exportCarInsurance,
  exportContract,
  getCarInsuranceList,
  getEmployeeList,
} from './services';
import type { IcarInsuranceItem, IcontractItem, IgetCarInsuranceListParams } from './type';
import { getRandomUUID, receiveOrAbandonOrReassignActionRequest } from './utils';

const CarInsuranceList = () => {
  const actionRef = useRef<ActionType>();
  const { initialState } = useModel<any>('@@initialState');
  const { currentUser } = initialState;
  const formRef = useRef<ProFormInstance>();
  const [form] = Form.useForm();
  const [employeeList, setEmployeeList] = useState<any[]>([]);
  const access = useAccess();

  const showErrorConTractListJsx = (errorContractList: IcontractItem[]) => {
    if (errorContractList?.length) {
      // 展示出错误的地址出来 有些oss地址可能有问题
      Modal.warning({
        width: 1000,
        content: (
          <div>
            <div>如下数据下载失败,请联系开发人员核实!!!</div>
            <ProTable
              // ossPath,contractName,contractNo,orderNo‘
              size={'small'}
              search={false}
              toolbar={{
                settings: [],
              }}
              pagination={{
                defaultPageSize: 10,
              }}
              dataSource={errorContractList}
              columns={[
                {
                  dataIndex: 'ossPath',
                  width: 300,
                  title: '合同地址',
                  ellipsis: true,
                  copyable: true,
                },
                { dataIndex: 'contractName', title: '合同名称' },
                { dataIndex: 'contractNo', title: '合同编号' },
                { dataIndex: 'orderNo', title: '订单号' },
              ]}
            />
          </div>
        ),
      });
    }
  };

  // 获取运营列表
  useEffect(() => {
    getEmployeeList().then((res) => {
      setEmployeeList(res?.data || []);
    });
  }, []);

  // 重新分派弹窗
  const showReassignModal = (record: IcarInsuranceItem) => {
    Modal.confirm({
      title: '重新分派',
      icon: null,
      centered: true,
      destroyOnClose: true,
      content: (
        <Form form={form}>
          <Form.Item
            label="分派给"
            name="pid"
            rules={[{ required: true, message: '请选择分派对象' }]}
          >
            <Select
              showSearch
              optionFilterProp="label"
              options={
                employeeList?.map((item) => ({ label: item.employeeName, value: item.pid })) || []
              }
            />
          </Form.Item>
        </Form>
      ),
      onCancel: () => {
        form.resetFields(); // 关闭弹窗时 清空表单
      },
      onOk: () => {
        // return 一个promise状态可以决定弹窗是否关闭
        return form.validateFields().then((values) => {
          // 重新分派 分配其他运营
          return receiveOrAbandonOrReassignActionRequest(
            record?.status,
            {
              orderNo: record?.orderNo,
              operatorUserId: values?.pid,
            },
            'reassign',
          ).then(() => {
            message.success('操作成功');
            // 重新分派完后 更新列表
            form.resetFields();
            actionRef.current?.reload();
          });
        });
      },
    });
  };

  async function getZipFileBlob(contractList: IcontractItem[], errorContractList: IcontractItem[]) {
    const zipWriter = new ZipWriter(new BlobWriter('application/zip'));
    await Promise.all(
      contractList.map(async (item) => {
        const { orderNo, contractName, contractNo, ossPath } = item;
        const response = await fetch(ossPath);
        const { body, status } = response || {};
        if (status === 200) {
          return zipWriter.add(
            `${orderNo}-${contractName}-${getRandomUUID(8)}.pdf`,
            // 用zip库的HttpReader会有问题
            // new HttpReader(ossPath, {
            //   useXHR: true,
            // }),
            // new HttpReader 返回的是可读流
            body || undefined,
          );
        } else {
          // 其他状态码 要提示出错误来
          errorContractList.push({
            ossPath,
            contractName,
            contractNo,
            orderNo,
          });
          return;
        }
      }),
    );
    return zipWriter.close();
  }

  function downloadFile(blob: Blob | MediaSource) {
    const url = URL.createObjectURL(blob);
    const a = Object.assign(document.createElement('a'), {
      download: '车险订单合同.zip',
      href: url,
    });
    a.click();
    URL.revokeObjectURL(url);
  }
  async function getSearchDataTotal() {
    const searchParams = filterProps(formRef.current?.getFieldsFormatValue?.());
    const data = await getCarInsuranceList({
      ...searchParams,
    });
    return data?.total;
  }
  return (
    <>
      <HeaderTab />
      <PageContainer>
        <ProTable<IcarInsuranceItem>
          columns={getColumns(access, currentUser, employeeList, actionRef, showReassignModal)}
          actionRef={actionRef}
          formRef={formRef}
          cardBordered
          search={{ defaultCollapsed: false }}
          request={async (params = {}) => {
            const { current: page, pageSize: size } = params;
            delete params.current;
            delete params.pageSize;
            const params1 = filterProps<IgetCarInsuranceListParams>({
              // channelCode,
              ...params,
              page,
              size,
            });
            const data = await getCarInsuranceList(params1);
            return {
              data: data.data,
              success: true,
              total: data.total,
            };
          }}
          // dataSource={[{ orderNo: '03032024081616313533072', carInfoList: [], status: 71 }]}
          editable={{
            type: 'multiple',
          }}
          scroll={{ x: 'max-content' }}
          rowKey="id"
          toolBarRender={() => [
            <Button
              key="add"
              onClick={() => {
                history.push('/businessMng/car-insurance/detail');
              }}
              type="primary"
            >
              新建
            </Button>,
            <AsyncExport
              key="export"
              getSearchDataTotal={getSearchDataTotal}
              getSearchParams={() => {
                const params = formRef.current?.getFieldsFormatValue?.();
                return removeBlankFromObject(
                  filterProps({
                    ...params,
                  }),
                );
              }}
              trigger={<Button type="primary">导出</Button>}
              exportAsync={exportCarInsurance}
              taskCode={[ItaskCodeEnValueEnum.INSURANCE_POLICY_ORDER_MGR]}
            />,
            // <LoadingButton
            //   key="LoadingButton1"
            //   onClick={async () => {
            //     try {
            //       const values = formRef.current?.getFieldsFormatValue?.();
            //       const params = {
            //         ...values,
            //       };
            //       const res = await exportCarInsurance(
            //         filterProps(params, ['createdAt', 'lendingTime']),
            //       );
            //       await downLoadExcel(res);
            //     } catch (error) {
            //       console.log('error', error);
            //     }
            //   }}
            //   type="primary"
            // >
            //   导出
            // </LoadingButton>,
            <LoadingButton
              key="LoadingButton2"
              onClick={async () => {
                try {
                  const values = formRef.current?.getFieldsFormatValue?.();
                  // 排除掉一些 undefined null '' 排除 自定义的属性 createdAt
                  const params = filterProps(
                    {
                      ...values,
                    },
                    ['createdAt', 'lendingTime'],
                  );
                  const data = await exportContract(params);
                  const contractList = Object.values(data).flat(2);
                  if (!contractList.length) return;
                  const errorContractList: IcontractItem[] = [];
                  const blob = await getZipFileBlob(contractList, errorContractList);
                  showErrorConTractListJsx(errorContractList);
                  downloadFile(blob);
                } catch (error) {
                  console.log('error', error);
                  Modal.error({
                    content: (
                      <div>
                        <h3>下载有误，请联系开发人员核实</h3>
                        <div>
                          <Tag color="error">{error?.message}</Tag>
                        </div>
                      </div>
                    ),
                  });
                }
              }}
            >
              导出合同
            </LoadingButton>,
          ]}
        />
      </PageContainer>
    </>
  );
};
export default memo(CarInsuranceList);
