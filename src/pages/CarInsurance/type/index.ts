import type { LENDING_MODEL_MAP } from '@/enums';
import type {
  IcompanyItem,
  IenterpriseItem,
  IproductItem,
} from '@/pages/CarInsuranceChannel/services';
import type { EcarInsuranceStatus, UloanUserType } from '@/utils/bankend/enum';
import { needPollingOrderStatus } from '@/utils/bankend/enum';
import type { UploadFile } from 'antd';

// 重新导出，保持向后兼容性
export { needPollingOrderStatus };

/**
 * 查询车险订单的入参
 */
export interface IgetCarInsuranceListParams {
  certiNo?: string; // 统一信用代码
  channelName?: string; // 渠道全称
  companyName?: string; // 公司名称
  createEndTime?: string; // 创建结束时间
  createStartTime?: string; // 开始创建时间
  orderNo?: string; // 贷款订单号
  orderStatus?: number; // 订单状态
  plateNo?: string; // 车牌号
  vin?: string; // 车架号
  page?: number;
  size?: number;
  channelCode?: string; // 用户id
}

/**
 * 详情信息 包括接口返回的 和 自己定义的
 */
export interface IcarInfoItem {
  assuredName: string; // 被投保人姓名
  commercialInsuranceEndDate: string; // 商业险结束日期
  commercialInsurancePeriod: number; // 商业险保单期限
  commercialInsurancePremium: number; // 商业险保费(元)
  commercialInsuranceStartDate: string; // 商业险开始日期
  compulsoryInsurancePremium: number; // 交强险保费(元)
  engineNumber: string; // 发动机号
  otherPaymentAmount: number; // 其他代付金额(元)
  plateNo: string; // 车牌号
  vehicleLicenseOwner: string; // 行驶证车主
  vehicleTaxAmount: number; // 车船税金额(元)
  vin: string; // 车架号
  id?: string | number | undefined; // 自增id // 编辑 新增

  // 二期优化后 新单这个值肯定是null
  insuranceSlipOssUrl?: string; // 投保单Oss相对url
  insuranceSlipUrl?: string; // 投保单url
  vehicleLicenseOssUrl?: string; // 车辆行驶证/登记证/合格证Oss相对url
  vehicleLicenseUrl?: string; // 车辆行驶证/登记证/合格证url

  vehicleLicenseUrlList: string[]; // 驾驶证 列表
  insuranceSlipUrlList: string[]; // 投保单列表
  vehicleLicenseOssUrlList: string[]; // 驾驶证 列表
  insuranceSlipOssUrlList: string[]; // 投保单列表

  carStatus?: number; // 车辆状态
  inspected: 0 | 1; // 车辆状态 0：未检查，1：已检查

  deleteFlag?: boolean; // 是否删除
  uuid?: string; //
  error?: Record<string, any>; // 错误信息
  vehicleLicenseUrlFileList?: UploadFile[];
  insuranceSlipUrlFileList?: UploadFile[];
}

export interface IcarInsuranceItem {
  applyAmount: number; // 总保费金额
  carInfoList: IcarInfoItem[]; // 车辆信息;
  carQuantity: number; // 车辆信息
  channelName: string; // 渠道名称
  companyName: string; // 公司名称
  downPaymentsDTO: {
    downPayment: string;
    paymentDate: string;
    paymentDocumentOssUrl: string;
    paymentDocumentUrl: string;
    onlinePayment: boolean; // 是否为线上支付
  }; // 首付款
  orderNo: string; // 贷款订单号
  processRecord: {
    // 审核记录
    processName: string; // 审核人
    processTime: string; // 审核时间
  };
  operatorUserId: string; // 操作人pid
  repayAmount: number; // 分期金额
  status: number; // 状态
  term: number; // 贷款期限
  orgCode: string; // 信用代码
}

export type UcarInsuranceStatus = keyof typeof EcarInsuranceStatus;
export type UcarInsuranceOperate = keyof typeof ECARINSURANCE_OPERATE_ACTION;

export type ToperateMap = Partial<Record<UcarInsuranceStatus, UcarInsuranceOperate[]>>;

/*
  车险操作行为枚举
*/
export enum ECARINSURANCE_OPERATE_ACTION {
  CLOSED = 'CLOSED', // 关闭
  DELETE_DRAFT = 'DELETE_DRAFT', // 删除草稿
  SAVE_DRAFT = 'SAVE_DRAFT', // 保存草稿
  MONTHLY_CALCU = 'MONTHLY_CALCU', // 月供测算
  SUBMIT_FOR_REVIEW = 'SUBMIT_FOR_REVIEW', // 提交审核
  SUBMIT_FOR_REVIEW_REJECT = 'SUBMIT_FOR_REVIEW_REJECT', // 驳回
  SUBMIT_PASS = 'SUBMIT_PASS', // 审核通过
  WITHDRAW_APPLICATION = 'WITHDRAW_APPLICATION', // 取消申请
  SIGN_QR_CODE = 'SIGN_QR_CODE', // 签约二维码
  DOWN_PAYMENTS_QR_CODE = 'DOWN_PAYMENTS_QR_CODE', // 首付二维码
  UPLOAD_DOWN_PAYMENTS_VOUCHER = 'UPLOAD_DOWN_PAYMENTS_VOUCHER', // 上传首付凭证
  UPLOAD_DOWN_CHECK_PASS = 'UPLOAD_DOWN_CHECK_PASS', // 首付审核通过
  DOWN_PAYMENTS_REJECT = 'DOWN_PAYMENTS_REJECT', // 首付审核驳回
  CHECK_VEHICLE = 'CHECK_VEHICLE', // 校验车辆
  SIGN_SUCCESS = 'SIGN_SUCCESS', // 校验车辆
}

/**
 * 车险订单操作
 * 不同的状态有不同的操作
 * 企业和个人公共的操作
 */
export const OperateMap: ToperateMap = {
  // 1草稿 : ["关闭","删除草稿","保存草稿","月供测算","提交审核"]
  DRAFT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.DELETE_DRAFT,
    ECARINSURANCE_OPERATE_ACTION.SAVE_DRAFT,
    ECARINSURANCE_OPERATE_ACTION.MONTHLY_CALCU,
    ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW,
  ],
  // 2已驳回: ["关闭","保存草稿","提交审核","月供测算",]
  REJECT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.SAVE_DRAFT,
    ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW,
    ECARINSURANCE_OPERATE_ACTION.MONTHLY_CALCU,
  ],
  // 3待审核: ["关闭","取消申请","驳回","审核通过",]
  PENDING: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
    ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW_REJECT,
    ECARINSURANCE_OPERATE_ACTION.SUBMIT_PASS,
  ],
  // 4待签约： ["关闭","取消申请","签约二维码",]
  SIGN: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
    ECARINSURANCE_OPERATE_ACTION.SIGN_QR_CODE,
  ],
  // 5待首付: 个人 ["关闭","取消申请","上传首付凭证","首付二维码"]   企业 ["关闭","取消申请","上传首付凭证",]
  // TO_BE_DOWN_PAYMENT: [
  //   ECARINSURANCE_OPERATE_ACTION.CLOSED,
  //   ECARINSURANCE_OPERATE_ACTION.DOWN_PAYMENTS_QR_CODE,
  //   ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
  //   ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_PAYMENTS_VOUCHER,
  // ],
  // 6待首付审核: ["关闭","取消申请","首付审核审核通过","首付审核驳回"]
  TO_BE_DOWN_PAYMENT_AUDIT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
    ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_CHECK_PASS,
    ECARINSURANCE_OPERATE_ACTION.DOWN_PAYMENTS_REJECT,
  ],
  //7 首付款驳回 ["关闭","取消申请","上传首付凭证"]
  TO_BE_DOWN_PAYMENT_REJECT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
    ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_PAYMENTS_VOUCHER,
  ],
  // 8 待放款: ["关闭","取消申请"]
  LOAN_PENDING: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
  ],
  // 9还款中: ["关闭",]
  REPAYING: [ECARINSURANCE_OPERATE_ACTION.CLOSED],
  // 10已取消
  CANCELED: [ECARINSURANCE_OPERATE_ACTION.CLOSED],
  //11已逾期
  OVERDUE: [ECARINSURANCE_OPERATE_ACTION.CLOSED],
  // 12已结清
  SETTLE: [ECARINSURANCE_OPERATE_ACTION.CLOSED],
  //13已退保
  SURRENDERED: [ECARINSURANCE_OPERATE_ACTION.CLOSED],
  // 14待领取 ["关闭","取消申请"]
  TO_BE_COLLECTED: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
  ],
  // 已失效
  INVALIDATION: [ECARINSURANCE_OPERATE_ACTION.CLOSED],
};

//
// 个人 -- 特殊的项目
export const PersonalOperateMap: ToperateMap = {
  TO_BE_DOWN_PAYMENT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.DOWN_PAYMENTS_QR_CODE,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
    ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_PAYMENTS_VOUCHER,
  ],
};

//  企业 -- 特殊的项目
export const CompanyOperateMap: ToperateMap = {
  TO_BE_DOWN_PAYMENT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
    ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_PAYMENTS_VOUCHER,
  ],
};

/**
 * 渠道用户对应的操作
 */
export const ChannelUserOperateMap: ToperateMap = {
  // 1草稿 : ["关闭","保存草稿","月供测算","提交审核"]
  DRAFT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.SAVE_DRAFT,
    ECARINSURANCE_OPERATE_ACTION.MONTHLY_CALCU,
    ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW,
  ],
  // 2 审核驳回: ["关闭","保存草稿","提交审核","月供测算",]
  REJECT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.SAVE_DRAFT,
    ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW,
    ECARINSURANCE_OPERATE_ACTION.MONTHLY_CALCU,
  ],
  // 3待审核: ["关闭","取消申请","驳回","审核通过",]
  PENDING: [ECARINSURANCE_OPERATE_ACTION.CLOSED, ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION],
  // 4待签约： ["关闭","取消申请","签约二维码",]
  SIGN: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
    ECARINSURANCE_OPERATE_ACTION.SIGN_QR_CODE,
  ],
  // 5待首付: ["关闭","取消申请","上传首付凭证",]
  // TO_BE_DOWN_PAYMENT: [
  //   ECARINSURANCE_OPERATE_ACTION.CLOSED,
  //   ECARINSURANCE_OPERATE_ACTION.DOWN_PAYMENTS_QR_CODE,
  //   ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
  //   ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_PAYMENTS_VOUCHER,
  // ],
  // 6待首付审核: ["关闭","取消申请"] 少了 "首付审核审核通过","首付审核驳回"
  TO_BE_DOWN_PAYMENT_AUDIT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
  ],
  //7 首付款驳回
  TO_BE_DOWN_PAYMENT_REJECT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
    ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_PAYMENTS_VOUCHER,
  ],
  // 8待放款: ["关闭", "取消申请",]
  LOAN_PENDING: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
  ],
  // 9还款中: ["关闭",]
  REPAYING: [ECARINSURANCE_OPERATE_ACTION.CLOSED],
  // 10已取消
  CANCELED: [ECARINSURANCE_OPERATE_ACTION.CLOSED],
  //11已逾期
  OVERDUE: [ECARINSURANCE_OPERATE_ACTION.CLOSED],
  // 12已结清
  SETTLE: [ECARINSURANCE_OPERATE_ACTION.CLOSED],
  //13已退保
  SURRENDERED: [ECARINSURANCE_OPERATE_ACTION.CLOSED],
  // 14待领取  ["关闭","取消申请"]
  TO_BE_COLLECTED: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
  ],
};

// 渠道用户
// 个人 -- 特殊的项目
export const PersonalChannelUserOperateMap: ToperateMap = {
  TO_BE_DOWN_PAYMENT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.DOWN_PAYMENTS_QR_CODE,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
    ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_PAYMENTS_VOUCHER,
  ],
};

// 企业 -- 企业特殊的项目
export const CompanyChannelUserOperateMap: ToperateMap = {
  TO_BE_DOWN_PAYMENT: [
    ECARINSURANCE_OPERATE_ACTION.CLOSED,
    ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
    ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_PAYMENTS_VOUCHER,
  ],
};

export interface IproductInfo {
  bait: number; // 保证金
  commission: number; // 手续费
  downPayment: number; // 首付
  downPaymentType: 1 | 2; // 1 比例 2 值
  productCode: string; //
  productName: string;
  repaymentDate: string[]; // 还款日
  repaymentMode: string; // 还款方式
  term: string; // 期限
  updateFlag: true;
  interestRate: number; // 年利率
}

export interface IloanPersonalInfo {
  name: number; // 姓名
  idNo: number; // 身份证号
  phone: number; // 手机号
  province: string; // 省份
  city: string; // 城市
  address: string; // 详细地址
  identityCardUrl: string; // 身份证正面相对url
  reverseIdentityCardUrl: string; // 身份证相对反面url
  identityCardLongUrl: string; // 身份证正面url
  reverseIdentityCardLongUrl: string; // 身份证反面url
}

export interface IbaseInfo {
  borrowerIdentity: string;
  borrowerType: UloanUserType;
  channelCode: string;
  channelName: string;
  channelType: string;
  epAuthNo: string;
  externalUserId?: string | null;
}
export interface IenterpriseInfo {
  authorizerName: string;
  enterpriseName: string;
  epAuthNo: string;
  orgCode: string;
  stakeholderId: string; // 缓存授权人ID
}
export interface IcompanyInfo {
  mergePayFlag?: string | null; // 是否合并打款
  commercialInsurancePremium: number;
  insuranceCompany: string;
  insuranceCompanySource: string;
  otherPaymentAmount: number;
  paymentAccountNo: string;
  policyHolder: string;
  subBranchName: string;
  sumInsurancePremium: number;
  bankName: string;
  bankNo: string;
}

/**
 * 提交时 需要传的车辆信息
 */
export interface IcarInfoDTOListParams {
  assuredName: string; // 被投保人姓名
  commercialInsuranceEndDate: string; // 商业险结束日期
  commercialInsurancePeriod: number; // 商业险保单期限
  commercialInsurancePremium: number; // 商业险保费(元)
  commercialInsuranceStartDate: string; // 商业险开始日期
  compulsoryInsurancePremium: number; // 交强险保费(元)
  engineNumber: string; // 发动机号
  otherPaymentAmount: number; // 其他代付金额(元)
  plateNo: string; // 车牌号
  vehicleLicenseOwner: string; // 行驶证车主
  vehicleTaxAmount: number; // 车船税金额(元)
  vin: string; // 车架号
  id?: string | number | undefined; // 自增id // 编辑 新增
  vehicleLicenseOssUrlList: string[]; // 车辆证件 相对路径
  insuranceSlipOssUrlList: string[]; // 投保材料 相对路径
  deleteFlag?: boolean; // 是否删除 标识此辆车是否被删除 要传给后端 后端从数据库中删除
}

export interface IsubmitData {
  baseInfo: IbaseInfo;
  carInfoDTOList: IcarInfoDTOListParams[];
  downPaymentsDTO: {
    paymentDate: string;
    paymentDocumentOssUrl: string;
    paymentDocumentUrl: string;
  };
  enterpriseInfo: IenterpriseInfo;
  insuranceInfo: IcompanyInfo;
  orderNo?: string;
  productInfo: IproductInfo;
  SUBMIT_FOR_REVIEW_REJECTMsg?: string; // 点击驳回需要填写驳回原因
  userBehaviorEnum: UcarInsuranceOperate;
}

export interface IoperateRecordItem {
  status: EcarInsuranceStatus; // 某次操作最终的状态
  statusDesc: string; // 状态描述
  approvalMsg: string; // 驳回内容
  approvalOperator: string; // 操作人
  auditTime: string; // 操作时间
}

export interface IotherInfoItem {
  fileName: string;
  netWorkPath: string;
  shortOssUrl: string;
}
/**
 * 详情信息
 */

export interface IdetailData {
  baseInfo: IbaseInfo;
  operatorUserId: string; // 操作人pid
  carInfoDTOList: IcarInfoItem[];
  lendingModel: keyof typeof LENDING_MODEL_MAP; //放款方式， 1.线上 2.线下
  downPaymentsDTO: {
    paymentDate?: string;
    paymentDocumentOssUrl?: string;
    paymentDocumentUrl?: string;
    paymentDocumentUrlList: string[];
    paymentDocumentOssUrlList: string[];
  };
  orderNo: string; // 订单号
  enterpriseInfo: IenterpriseInfo;
  insuranceInfo: IcompanyInfo;
  productInfo: IproductInfo;
  personalInfo: IloanPersonalInfo;
  orderStatus: EcarInsuranceStatus; // 当前状态
  operateLogs: IoperateRecordItem[];
  shortUrlInfo: {
    apiRelPath: string; //API相对地址
    hostUrl: string; // 服务地址
    httpMethod: string; // Http请求方式：1、GET方法；2、POST方法
    longUrl: string; // 长链接
    paramMap: string; // 参数与值
    shortUrl: string; // 短链接
    type: string; // 短链类型：1 车险分期
    phone: string;
  };
  otherInformationList: IotherInfoItem[];
  contractInfo?: {
    fileDesc?: string;
    filePath?: string;
    netWorkPath?: string;
  };
  repayInfo: {
    interestAmount: string; // 利息总额
    repayAmount: string; // 还款总额
    actualDownPaymentAmount: string; // 实际首付金额
    loanAmount: string; // 借款总额
  };
}

export type TfileType = 'image/jpg' | 'image/jpeg' | 'image/png' | 'image/webp' | 'application/pdf';
export interface IcarUploadFileItem {
  uid: string;
  name: string; // 文件名称
  // filePath: string // 相对路径
  url: string;
  type: TfileType;
}

/**
 * 上传excel后的车辆信息
 */
export interface IuploadCarInfoItem {
  commercialInsuranceEndDate: string; // 商业险结束日期（必填，需晚于商业险开始日期）
  commercialInsurancePremium: number; // 商业险保费（必填，需要为0-40000之间，最多两位小数点）
  commercialInsuranceStartDate: string; // 商业险开始日期（必填，需晚于当前日期）
  compulsoryInsurancePremium: number; // 交强险保费（必填，需要为0-40000之间，最多两位小数点，没有则填0）
  engineCode: string; // 发动机号（必填，不得含有特殊符号）
  errMsgMap: Record<string, any>; // 错误信息map，key是字段名，如：vehicleNumber，value是错误msg
  id: number; // 序号
  insuredName: string; // 被保险人名称（必填，需要与投保单上保持一致）x w小数点，没有则填0）
  vehicleLicenseOwner: string; // 行驶证车主名称（必填，需要与行驶证上保持一致）
  vehicleNumber: string; // 车牌号/新车合格证号（必填，不得含有特殊符号）
  vehicleTaxAmount: number; // 车船税金额（必填，需要为0-40000之间，最多两位小数点，没有则填0）
  vin: string; // 车架号（必填，需为17位且不得含有空格/O/Q/I/特殊符号）

  vehicleLicenseUrlList: string[]; // 驾驶证 列表
  insuranceSlipUrlList: string[]; // 投保单列表

  insuranceSlipOssUrlList: string[];
  vehicleLicenseOssUrlList: string[];

  otherPaymentAmount: number; // 其他代付金额
}

/**
 * 车辆信息map
 */
export const carInfoMap = {
  commercialInsuranceEndDate: '商业险结束日期',
  commercialInsurancePremium: '商业险保费',
  commercialInsuranceStartDate: '商业险开始日期',
  compulsoryInsurancePremium: '交强险保费',
  engineCode: '发动机号',
  errMsgMap: {},
  id: 'id',
  insuredName: '被保险人名称',
  otherPaymentAmount: '其他代付金额',
  vehicleLicenseOwner: '行驶证车主名称',
  vehicleNumber: '车牌号/新车合格证号',
  vehicleTaxAmount: '车船税金额',
  vin: '车架号',
};

/**
 * 上传多个影像资料 响应结果
 */
export interface IuploadImgItem {
  errMsg: string;
  fileName: string;
  filePath: string;
  netWorkPath: string;
}

/**
 * 月供测算入参数
 */
export interface ImonthlyCalcuParams {
  businessInsuranceAmount: number; // 商业险保费 所有车辆的保费之和
  carInfoDTOList: IcarInfoItem[];
  downPayment: number; // 首付值
  downPaymentType: number; // 首付类型 1是固定比例 2 是固定金额
  productCode: string; // 产品三级code
  totalTerm: number; // 还款期限
}

export interface IrepayPlanItem {
  amount: number; // 本期总额
  interest: number; // 利息
  principal: number; // 本金
  repayDate: string; // 还款日期
  term: number; // 期数
}
/**
 * 月供测算响应结果
 */
export interface ImonthlyCalcuRes {
  downPaymentAmount: number; // 首付金额
  interestAmount: number; // 利息总额
  orderAmount: number; // 借款总金额
  productCode: string; // 产品三级code
  repayAmount: number; // 应还总金额
  repayPlanList: IrepayPlanItem[]; // 还款计划
  totalTerm: number; // 还款总期限
}

export interface IcarChannelItem {
  id: 2;
  channelCode: string; // 渠道编码
  channelType: string; // 渠道类型
  channelShortName: string; // 渠道简称
  channelName: string; // 渠道全称
  orgCode: string; // 统一信用代码
  contactName: string; // 联系人姓名
  contactPhone: string; // 联系人电话
  contactAddress: string; // 联系人地址
  channelAccountNo: number; // 渠道账号ID
  channelUserAccount: number; // 渠道账号，展示用
  relatedProduct: number[]; // 关联的产品列表
  relatedEnterprise: number[]; // 关联的企业列表
  relatedPayAccount: number[]; // 关联的收款公司列表
  companyList: IcompanyItem[];
  enterpriseList: IenterpriseItem[];
  productList: IproductItem[];
  updatedAt: string;
  createdAt: string;
  personalCompanyList: IcompanyItem[];
  personalProductList: IproductItem[];
  personalRelatedProduct: number[]; // 关联的产品列表（个人）
  personalRelatedPayAccount: number[]; // 关联的收款公司列表（个人）
}

export interface IoptionsData {
  channelList: IcarChannelItem[];
  enterpriseList: IenterpriseItem[];
  productList: IproductItem[];
  companyList: IcompanyItem[];
}

// 缓存的全局性的变量, 不受react更新的影响, 不需要维护的状态
export interface IrefData {
  rejectMsg: string; // 拒绝原因
  channelIsOnChange: boolean; // 渠道是否变化了  变化了置 为 true 会做一些操作 操作然后再置 为 false
}

export interface IcontractItem {
  orderNo: string;
  contractName: string;
  contractNo: string;
  ossPath: string;
  body?: ReadableStream;
}
// export  interface FeeItem = { costType: string; remissionAmount: number }[];
// 导出合同
export type IexportContractRes = Record<string, IcontractItem[]>;

export type Icity = { name: string; children: { name: string }[] }[];

export enum LEVEL {
  FIRST_CHANNEL = 1,
  SECOND_CHANNEL = 2,
}

export interface IOrderActionParams {
  orderNo: string; // 订单号
  operatorUserId: string; // 操作人pid
}
