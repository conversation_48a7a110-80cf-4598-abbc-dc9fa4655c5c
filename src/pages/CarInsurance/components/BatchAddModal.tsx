import { getAuthHeaders } from '@/utils/auth';
import { downLoadExcel } from '@/utils/utils';
import { useModel } from '@umijs/max';
import {
  Button,
  message,
  Modal,
  Space,
  Spin,
  Steps,
  Table,
  Tabs,
  Tag,
  Typography,
  Upload,
} from 'antd';
import type { UploadChangeParam } from 'antd/lib/upload';
import dayjs from 'dayjs';
import type { ReactElement } from 'react';
import React, { memo, useEffect, useState } from 'react';
import { downLoadCarInfoTemplate } from '../services';
import type { IcarInfoItem, IuploadCarInfoItem } from '../type';
import { carInfoMap } from '../type';
import { getBaseUploadAction, getRandomUUID, verifyCarInfo } from '../utils';
import CarUploadDragger from './CarUploadDragger';
const { Text } = Typography;
const { Step } = Steps;
type Props = {
  children: ReactElement;
};
const BatchAddModal: React.FC<Props> = (props) => {
  const [current, setCurrent] = useState(0);
  const { setCarInfo, carInfo } = useModel('CarInsurance.carInsurance');
  const [visible, setVisible] = useState(false);
  const [excelErrorList, setExcelErrorList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [carInfoList, setCarInfoList] = useState<IuploadCarInfoItem[]>([]);
  const [disabledIgnoreError, setDisabledIgnoreError] = useState(false);
  const { children } = props;

  console.log('carInfoList', carInfoList);

  useEffect(() => {
    // 打开 和 关闭 都要 重置步骤
    setCurrent(0);
    setExcelErrorList([]);
    setCarInfoList([]);
  }, [visible]);
  const [downloading, setDownloading] = useState(false);

  const columns = [
    { key: 1, title: '序号', dataIndex: 'id' },
    { key: 2, title: '错误', dataIndex: 'error' },
  ];

  function downLoadTemplate() {
    setDownloading(true);
    downLoadCarInfoTemplate().then((res) => {
      downLoadExcel(res);
      setDownloading(false);
    });
  }

  function onExcelChange(info: UploadChangeParam) {
    if (info.file.status === 'uploading') {
      setLoading(true);
    }
    if (info.file.status === 'done') {
      setLoading(false);
      // 说明网络 状态码正常
      if (info.file.response?.ret === 0) {
        const carList: IuploadCarInfoItem[] = info?.file?.response?.data;
        // 把有错误的过滤出来
        const errCarList = carList.filter((item) => Object.keys(item.errMsgMap).length !== 0);
        let disabledIgnoreErrorNum = 0;
        const errs = errCarList.map((item) => {
          const { id, errMsgMap } = item;
          if (errMsgMap?.vin === '缺失' && errMsgMap?.vehicleNumber === '缺失') {
            disabledIgnoreErrorNum++;
          }
          return {
            id: id,
            error: Object.keys(errMsgMap).map((key) => (
              <Tag color="error">{carInfoMap[key] + '-' + errMsgMap[key]}</Tag>
            )),
          };
        });
        if (errs.length) {
          // 如果 错误中 所有数据 车架号和车牌号都不存在 则 不能点击忽略错误
          setDisabledIgnoreError(disabledIgnoreErrorNum === carList.length);
          setExcelErrorList(errs);
        } else {
          message.success('上传成功');
          setExcelErrorList([]);
          setCurrent(1);
        }
        setCarInfoList(
          carList.map((item) => ({
            ...item,
            insuranceSlipUrlList: [],
            vehicleLicenseUrlList: [],
            insuranceSlipOssUrlList: [],
            vehicleLicenseOssUrlList: [],
          })),
        );
      } else {
        // 自定义未知错误
        message.error(`上传失败-${info.file.response?.msg}`);
        setLoading(false);
      }
    } else if (info.file.status === 'error') {
      // 一般为网络错误
      message.error(`上传失败`);
      setLoading(false);
    }
  }
  function submit() {
    const carInfo1 = carInfoList.map((item, index) => {
      const {
        insuredName,
        commercialInsuranceEndDate,
        commercialInsuranceStartDate,
        engineCode,
        vehicleNumber,
        ...rest
      } = item;

      const commercialInsurancePeriod = dayjs(commercialInsuranceEndDate).diff(
        commercialInsuranceStartDate,
        'day',
      );
      const result: IcarInfoItem = {
        commercialInsurancePeriod,
        assuredName: insuredName,
        engineNumber: engineCode,
        plateNo: vehicleNumber,
        uuid: getRandomUUID(),
        inspected: index === 0 ? 1 : 0,
        commercialInsuranceEndDate,
        commercialInsuranceStartDate,
        ...rest,
        id: undefined,
      };
      return result;
    });
    // 如果再次批量新增 有id的要把id传给后端 没有id的直接删除
    const carInfo2 = carInfo?.filter((item) => {
      if (item?.id) {
        // 批量新增是全量覆盖
        item.deleteFlag = true; // 有id的要把id传给后端
        return true;
      } else {
        return false;
      }
    });
    const carInfo3 = verifyCarInfo([...carInfo2, ...carInfo1]);
    setCarInfo(carInfo3);
    setVisible(false);
  }
  function renderFooter() {
    if (current === 0) {
      return (
        <Space>
          <Button loading={downloading} type="link" onClick={downLoadTemplate}>
            下载表格模版
          </Button>
          <Upload
            action={getBaseUploadAction('/bizadmin/insurance/policy/order/uploadExcel')}
            onChange={onExcelChange}
            showUploadList={false}
            headers={{
              'hll-appid': 'bme-finance-bizadmin-svc',
              ...getAuthHeaders(),
            }}
            name="file"
          >
            <Button type="primary">{excelErrorList?.length ? '重新上传' : '上传表格'}</Button>
          </Upload>

          {excelErrorList?.length ? (
            <Button
              loading={downloading}
              type="primary"
              disabled={disabledIgnoreError}
              danger
              onClick={() => {
                setCurrent(1);
              }}
            >
              忽略错误
            </Button>
          ) : null}
        </Space>
      );
    } else if (current === 1) {
      return (
        <Button type="primary" onClick={submit}>
          提交
        </Button>
      );
    } else {
      return null;
    }
  }

  function renderStepOneJsx() {
    return (
      <>
        <div>
          <div style={{ color: 'red' }}>0.批量新增会覆盖已经存在的所有车辆信息</div>
          <div>1.车牌号/新车合格证号：不得含有特殊符号</div>
          <div>2.车架号：需为17位且不得含有空格/O/Q/I/特殊符号</div>
          <div>3.发动机号：不得含有特殊符号</div>
          <div>4.车主姓名/被保险人姓名：不能少于2个字</div>
          <div>5.商业险结束日期：不得早于开始日期，且不能晚于开始日期366天</div>
          <div>6.商业险保费/交强险保费/车船税/其他代付：需要为0-40000之间，最多两位小数点</div>
          <div>7.车牌号/车架号/发动机号不得重复</div>
        </div>
        <Table
          columns={columns}
          dataSource={excelErrorList}
          title={() => <div>表格校验结果</div>}
          pagination={false}
          bordered={true}
          size="small"
        />
      </>
    );
  }

  function renderStepTwoJsx() {
    return (
      <div>
        <h3>已识别车辆数量: {carInfoList?.length}</h3>
        <Tabs
          style={{ height: 500 }}
          tabPosition={'left'}
          items={carInfoList.map((car, index) => {
            const { vin, insuranceSlipUrlList, vehicleLicenseUrlList, vehicleNumber } = car;
            const num = (insuranceSlipUrlList?.length || 0) + (vehicleLicenseUrlList?.length || 0);
            return {
              label: (
                <div
                  style={{
                    textAlign: 'start',
                    display: 'flex',
                    alignItems: 'center',
                    gap: 10,
                  }}
                >
                  <div>{index + 1}</div>
                  <div>
                    <div>
                      <Text type="secondary">{vin ? vin : '无车架号'}</Text>
                    </div>
                    <div>
                      <Text type="secondary">{vehicleNumber ? vehicleNumber : '无车牌号'}</Text>

                      <span style={{ color: num === 0 ? 'red' : 'blue' }}>【 {num} 】</span>
                    </div>
                  </div>
                </div>
              ),
              //   key: getRandomUUID(),  // 表格内容不可控 每次渲染每次都不一样 会导致什么结果 如果用数组索引会有什么结果?
              key: index + '',
              children: (
                <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                  <div>
                    <div>车辆证件</div>
                    {/* 先以索引作为 id，因为表格的id vin vehicleNumber 都不可控  */}
                    <div>
                      <CarUploadDragger
                        type="vehicleLicenseUrlList"
                        ossType="vehicleLicenseOssUrlList"
                        setCarInfoList={setCarInfoList}
                        currentIndex={index}
                      />
                    </div>
                  </div>
                  <div>
                    <div>投保材料</div>
                    <div>
                      <CarUploadDragger
                        type="insuranceSlipUrlList"
                        ossType="insuranceSlipOssUrlList"
                        setCarInfoList={setCarInfoList}
                        currentIndex={index}
                      />
                    </div>
                  </div>
                </div>
              ),
            };
          })}
        />
      </div>
    );
  }

  return (
    <>
      <div
        onClick={() => {
          setVisible(true);
        }}
      >
        {children}
      </div>
      <Modal
        maskClosable={false}
        open={visible}
        destroyOnClose
        onCancel={() => {
          setVisible(false);
        }}
        footer={renderFooter()}
        title="批量新增"
        width={1000}
      >
        <Spin spinning={loading}>
          <Steps current={current} style={{ marginBottom: 10 }}>
            <Step title="上传表格" />
            <Step title="上传图片材料" />
          </Steps>
          {current === 0 ? renderStepOneJsx() : renderStepTwoJsx()}
        </Spin>
      </Modal>
    </>
  );
};

export default memo(BatchAddModal);
