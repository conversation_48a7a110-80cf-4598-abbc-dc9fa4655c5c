import type { IcompanyItem } from '@/pages/CarInsuranceChannel/services';
import { EinsuranceCompanySource } from '@/utils/bankend/enum';
import { isCarInsuranceStoreUser } from '@/utils/utils';
import ProForm from '@ant-design/pro-form';
import { useAccess, useModel } from '@umijs/max';
import type { FormInstance } from 'antd';
import { Descriptions, Form, Radio, Select } from 'antd';
import React, { memo, useEffect, useMemo, useState } from 'react';
import type { IcompanyInfo } from '../type';

import { EcarInsuranceStatus, EinsuranceSubject } from '@/utils/bankend/enum';

type Props = {
  companyInfoForm: FormInstance;
};

// 非可操作状态
const noEditableStatus = [EcarInsuranceStatus.DRAFT, EcarInsuranceStatus.REJECT];

const Company: React.FC<Props> = (props) => {
  const { companyInfoForm } = props;
  const formItemStyle = { width: 180 };
  const { optionsData, isEditable, carInfo, detailData } = useModel('CarInsurance.carInsurance');
  const status: EcarInsuranceStatus = detailData.orderStatus;

  const companyList: IcompanyItem[] = optionsData?.companyList;
  const [, setFlush] = useState<any>({});
  // 如果用户的角色还有channelUser 说明是渠道用户,否则为运营
  const access = useAccess();
  const isChannelUser = isCarInsuranceStoreUser(access);

  useEffect(() => {
    const { insuranceInfo, orderNo } = detailData;
    if (orderNo) {
      const { paymentAccountNo, insuranceCompany, subBranchName, bankNo, bankName } = insuranceInfo;
      console.log('insuranceInfo', insuranceInfo);
      const prefix = paymentAccountNo?.substring(paymentAccountNo?.length - 4);
      const newInsuranceInfo = {
        ...insuranceInfo,
        mergePayFlag: insuranceInfo?.mergePayFlag ?? false,
        // companyName: insuranceCompany ? `${insuranceCompany}-${prefix}` : undefined,
        companyName: insuranceCompany
          ? `${insuranceCompany}-${paymentAccountNo}-${subBranchName}-${bankNo}-${bankName}`
          : undefined,
      };
      companyInfoForm.setFieldsValue(newInsuranceInfo);
    }
  }, [companyInfoForm, detailData]);

  useEffect(() => {
    // 当carInfo发生变化 需要重新计算 金额
    console.log('carInfo111', carInfo);
    if (!carInfo?.length) {
      companyInfoForm.setFieldsValue({
        sumInsurancePremium: undefined,
        otherPaymentAmount: undefined,
        commercialInsurancePremium: undefined,
      });
    } else {
      const commercialInsurancePremium = carInfo.reduce((pre, cur) => {
        if (cur?.deleteFlag === true) {
          return pre;
        }
        return pre + parseFloat(cur.commercialInsurancePremium);
      }, 0);
      const otherPaymentAmount1 = carInfo.reduce((pre, cur) => {
        if (cur?.deleteFlag === true) {
          return pre;
        }
        const { compulsoryInsurancePremium, otherPaymentAmount, vehicleTaxAmount } = cur;
        return (
          pre +
          parseFloat(compulsoryInsurancePremium) +
          parseFloat(otherPaymentAmount) +
          parseFloat(vehicleTaxAmount)
        );
      }, 0);
      const sumInsurancePremium = commercialInsurancePremium + otherPaymentAmount1;
      companyInfoForm.setFieldsValue({
        sumInsurancePremium: sumInsurancePremium.toFixed(2),
        otherPaymentAmount: otherPaymentAmount1.toFixed(2),
        commercialInsurancePremium: commercialInsurancePremium.toFixed(2),
      });
      console.log('sumInsurancePremium', sumInsurancePremium);
    }

    // 这里强制更新是因为form.item的子元素不是input这种导致 companyInfoForm.setFieldsValue不生效
    setFlush({});
  }, [carInfo, companyInfoForm]);

  const insureInfo = [
    {
      label: '总保费金额(元)',
      value: 'sumInsurancePremium',
    },
    {
      label: '保费收款账号',
      value: 'paymentAccountNo',
    },
    {
      label: '商业保险总金额(元)',
      value: 'commercialInsurancePremium',
    },
    {
      label: '保费收款账号开户行',
      value: 'subBranchName',
    },
    {
      label: '其他代付保险总金额(元)',
      value: 'otherPaymentAmount',
    },
    // {
    //   label: '投保人',
    //   value: 'policyHolder',
    // },
  ];

  function onChange(value: string, options: any) {
    const params = {
      policyHolder: '广州货满满汽车咨询有限公司', // 目前是固定的
    };
    const { paymentAccountNo, subBranchName, bankNo, bankName, companyName } = options;
    companyInfoForm.setFieldsValue({
      paymentAccountNo,
      subBranchName,
      ...params,
      bankNo,
      bankName,
      insuranceCompany: companyName,
    });
    setFlush({});
  }
  function getValue(value: any) {
    console.log('value23', value);
    if (Number.isNaN(value) || value === 'NaN') {
      // 这里是严格判断 NaN === NaN window.isNaN 会尝试转数字 如果能则返回false
      // NaN 莫名其妙antd 转成字符串了
      return (
        <div>
          / <span style={{ color: 'red' }}>车辆信息金额输入异常</span>
        </div>
      );
    }
    return value || value === 0 ? value : <div>/</div>;
  }
  /* 草稿状态都不展示投保主体
      待审核状态，运营展示，渠道不展示投保主体
      后续的状态是两者都展示，不可编辑
      */
  function isShowMainSubject(status: number) {
    //渠道待审核不展示投保主体
    if (status === EcarInsuranceStatus.PENDING && isChannelUser) {
      return false;
    }
    //运营待审核展示投保主体
    if (status === EcarInsuranceStatus.PENDING && !isChannelUser) {
      return true;
    }
    //新建情况，没有status,默认是草稿状态，需要额外判断  审核拒绝和草稿状态不展示投保主体
    // console.log(status, '---status');
    if (status && !noEditableStatus.includes(status)) {
      return true;
    }
    return false;
  }

  // 是否展示合并打款
  const isShowMergePayment = useMemo(() => {
    // 当订单车辆数 > 1 且 订单状态为 待审核时，或者提交审核后所有状态都需要展示该信息，需要合并打款
    if (
      status &&
      (status === EcarInsuranceStatus.PENDING ||
        ![
          ...noEditableStatus,
          EcarInsuranceStatus.PENDING,
          EcarInsuranceStatus.TO_BE_COLLECTED,
        ].includes(status)) &&
      carInfo?.length > 1
    ) {
      return true;
    }
    return false;
  }, [carInfo, status]);

  // 是否允许更改合并打款选项
  const isAllowChangeMergePayment = useMemo(() => {
    // 待审核，可以操作更改此选项
    const allowEditStatus = [EcarInsuranceStatus.PENDING];
    // 只有运营或运营主管可以操作
    if (access.hasAccess('merge_pay_car_insurance_list') && allowEditStatus.includes(status)) {
      return true;
    }
    return false;
  }, [status, access]);

  return (
    <div>
      <h3>保险信息</h3>
      <ProForm<IcompanyInfo>
        layout={'horizontal'}
        form={companyInfoForm}
        initialValues={{ a: 2 }}
        scrollToFirstError={{
          block: 'center', // 滚动位置
          behavior: 'smooth', // 平滑滚动
        }}
        submitter={{
          render: () => null,
        }}
      >
        <ProForm.Group>
          <Form.Item label="保险收款公司名称" name={'companyName'} rules={[{ required: true }]}>
            <Select
              optionLabelProp={'companyName'}
              disabled={!isEditable}
              showSearch
              onChange={onChange}
              options={companyList.map((item) => {
                const { companyName, paymentAccountNo, subBranchName, bankNo, bankName, id } = item;
                const str = paymentAccountNo?.substring(paymentAccountNo?.length - 4);
                console.log('id112233', id);
                return {
                  label: (
                    <>
                      {companyName}
                      <span style={{ color: 'red' }}>({str})</span>
                    </>
                  ),
                  // value: `${companyName}-${str}`,
                  value: `${companyName}-${paymentAccountNo}-${subBranchName}-${bankNo}-${bankName}`,
                  paymentAccountNo,
                  subBranchName,
                  bankNo,
                  key: id, // value 出现了一样的，导致筛选有点问题
                  bankName,
                  companyName,
                };
              })}
              style={{ width: 400 }}
            />
          </Form.Item>
          <Form.Item
            label="保险公司来源"
            required
            name={'insuranceCompanySource'}
            rules={[{ required: true }]}
          >
            <Select
              disabled={!isEditable}
              options={Object.keys(EinsuranceCompanySource).map((item) => {
                return {
                  label: EinsuranceCompanySource[item],
                  value: item,
                };
              })}
              style={formItemStyle}
            />
          </Form.Item>

          {isShowMainSubject(status) ? (
            <Form.Item
              label="投保主体"
              required
              name={'insuranceSubject'}
              rules={[{ required: true }]}
            >
              <Select
                disabled={!(status === EcarInsuranceStatus.PENDING)} //运营只有待审核状态可以编辑
                options={Object.keys(EinsuranceSubject).map((item) => {
                  return {
                    label: EinsuranceSubject[item],
                    value: item,
                  };
                })}
                style={formItemStyle}
              />
            </Form.Item>
          ) : (
            ''
          )}
          {isShowMergePayment && (
            <Form.Item
              label="是否合并打款"
              // 只有待审核状态时，需要必填写此选项
              rules={[
                { required: status === EcarInsuranceStatus.PENDING, message: '请选择是否合并打款' },
              ]}
              name={'mergePayFlag'}
              tooltip="非运营人员无法操作此选项"
            >
              <Radio.Group
                defaultValue=""
                disabled={!isAllowChangeMergePayment}
                options={[
                  { label: '是', value: true },
                  { label: '否', value: false },
                ]}
              />
            </Form.Item>
          )}
        </ProForm.Group>

        <div style={{ width: 1000 }}>
          <Descriptions column={2}>
            {insureInfo.map((item) => {
              const { label, value } = item;
              return (
                <Descriptions.Item key={value} label={label}>
                  <Form.Item label={label} name={value} noStyle>
                    {getValue(companyInfoForm.getFieldValue(value))}
                  </Form.Item>
                </Descriptions.Item>
              );
            })}
          </Descriptions>
        </div>
        <Form.Item label={'联行号'} name={'bankNo'} style={{ display: 'none' }} />
        <Form.Item label={'银行名称'} name={'bankName'} style={{ display: 'none' }} />
        <Form.Item label={'保险公司名称'} name={'insuranceCompany'} style={{ display: 'none' }} />
      </ProForm>
    </div>
  );
};

export default memo(Company);
