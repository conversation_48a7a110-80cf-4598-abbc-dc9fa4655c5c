/* eslint-disable react-hooks/exhaustive-deps */
import { carStatusMap, EcarInsuranceStatus } from '@/utils/bankend/enum';
import { history, useModel } from '@umijs/max';
import { useDebounceFn } from 'ahooks';
import type { FormInstance } from 'antd';
import { Button, Empty, Space, Tabs, Tag } from 'antd';
import React, { memo, useEffect, useRef } from 'react';
import { updateCarInspected } from '../services';
import type { IcarInfoItem } from '../type';
import { getRandomUUID, verifyCarInfo } from '../utils';
import BatchAddModal from './BatchAddModal';
import CarDetail from './CarDetail';
import CarEdit from './CarEdit';

const { TabPane } = Tabs;
type Props = {
  companyInfoForm?: FormInstance;
};
const BaseInfo: React.FC<Props> = () => {
  const { carInfo, detailData, setCarInfo, isEditable } = useModel('CarInsurance.carInsurance');
  const refData = useRef<string[]>([]);
  const { run } = useDebounceFn(handleUpdateCheckStatus, { wait: 1000 });

  useEffect(() => {
    const { carInfoDTOList, orderNo } = detailData;
    const carInfoDTOList1 = carInfoDTOList?.map((item) => {
      return {
        ...item,
        uuid: item?.uuid ? item?.uuid : getRandomUUID(),
      };
    });
    verifyCarInfo(carInfoDTOList1);
    if (orderNo) setCarInfo(carInfoDTOList1);
  }, [detailData]);

  // 更新检查状态
  async function handleUpdateCheckStatus() {
    const { orderNo } = (history as any).location.query;
    if (orderNo && refData.current?.length)
      await updateCarInspected({ orderNo, vinList: refData.current, type: 1 });
    refData.current.length = 0;
  }

  function onTabClick(value: string) {
    if (detailData?.orderStatus === EcarInsuranceStatus.PENDING) {
      if (carInfo.find((item) => item.uuid === value)?.inspected === 1) {
        // 当前点击的已经 检查过了 就不需要再做任何事情
        return;
      } else {
        // 用户可能快速点击 所以做防抖处理 只有待审核状态 和 未检查的 车点击 才去更新检查状态
        // 未检查的点了哪个收集哪个
        refData.current.push(carInfo.find((item) => item.uuid === value)!.vin);
        run();
        const carInfo1 = carInfo.map((item) => {
          if (item.uuid == value) {
            item.inspected = 1;
          }
          return {
            ...item,
          };
        });
        setCarInfo(carInfo1);
      }
    }
  }
  return (
    <>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          borderBottom: '1px solid #f0f0f0',
        }}
      >
        <h3>
          车辆信息: <Tag color="success">{carInfo?.filter((item) => !item.deleteFlag).length}</Tag>
          辆
        </h3>
        <Space>
          <CarEdit title="单个新增">
            <Button type="link" disabled={!isEditable}>
              + 单个新增
            </Button>
          </CarEdit>
          <BatchAddModal>
            <Button type="link" disabled={!isEditable}>
              + 批量新增
            </Button>
          </BatchAddModal>
        </Space>
      </div>
      <div>
        <div className="car-main">
          {carInfo?.length ? (
            <Tabs
              tabPosition={'left'}
              style={{ maxHeight: 800 }} // 侧边栏内容过多会撑特别高 所以需要设置最大高度，出现滚动条
              onTabClick={(value) => {
                onTabClick(value);
              }}
            >
              {(carInfo as IcarInfoItem[])
                ?.filter((item) => !item.deleteFlag)
                .map((item, index) => {
                  const { uuid, vin, inspected, error, carStatus, plateNo } = item;
                  return (
                    <TabPane
                      key={uuid}
                      tab={
                        <div
                          style={{
                            display: 'flex',
                            gap: 10,
                            alignItems: 'center',
                          }}
                        >
                          <div style={{ color: '#8c8c8c' }}>{index + 1}</div>
                          <div>
                            <div style={{ display: 'flex', gap: 10 }}>
                              {vin ? vin : <span style={{ color: 'red' }}>车架号缺失</span>}
                              {error ? <span style={{ color: 'red' }}>异常</span> : null}
                              {!inspected &&
                              detailData?.orderStatus === EcarInsuranceStatus.PENDING ? (
                                <div style={{ color: 'red' }}>未检查</div>
                              ) : null}
                              {detailData?.orderStatus === EcarInsuranceStatus.REPAYING ? (
                                <span>【{carStatusMap[carStatus!]}】</span>
                              ) : null}
                            </div>
                            <div style={{ color: '#bfbfbf' }}>{plateNo}</div>
                          </div>
                        </div>
                      }
                    >
                      <CarDetail carInfoItem={item} key={Math.random()} />
                    </TabPane>
                  );
                })}
            </Tabs>
          ) : (
            <Empty />
          )}
        </div>
      </div>
    </>
  );
};
export default memo(BaseInfo);
