import { getAuthHeaders } from '@/utils/auth';
import { getBaseUrl } from '@/utils/utils';
import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import { Button, Image, message, Upload } from 'antd';
import type { UploadChangeParam } from 'antd/es/upload';
import type { RcFile, UploadFile, UploadProps } from 'antd/es/upload/interface';
import React, { useEffect, useState } from 'react';
import styles from '../styles/index.less';
const { Dragger } = Upload;
type Props = {
  longUrl: string;
  onChange?: (val: string) => void;
};
const UploadImage: React.FC<Props> = (props) => {
  const { onChange, longUrl } = props;
  const [loading, setLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [imageUrl, setImageUrl] = useState<string>(longUrl);
  const { isEditable } = useModel('CarInsurance.carInsurance');

  useEffect(() => {
    setImageUrl(longUrl);
  }, [longUrl]);

  const handleChange: UploadProps['onChange'] = (info: UploadChangeParam<UploadFile>) => {
    if (info.file.status === 'uploading') {
      setLoading(true);
      return;
    }
    if (info.file.status === 'done') {
      const { code, data = {} } = info.file.response || {};
      const { filePath, netWorkPath, errMsg } = data;
      if (code === 200) {
        setImageUrl(netWorkPath);
        onChange?.(filePath);
        message.success('上传成功');
      } else {
        message.error('上传有误' + errMsg);
      }
      setLoading(false);
    }
  };

  function beforeUpload(file: RcFile) {
    console.log('filefilefilefile', file);
    const format = ['image/jpg', 'image/jpeg', 'image/png'];
    if (!format.includes(file?.type)) {
      message.error('格式为仅支持.jpg');
      return false || Upload.LIST_IGNORE;
    } else if (file?.size > 5 * 1024 * 1024) {
      message.error('图片需小于5M');
      return false || Upload.LIST_IGNORE;
    } else {
      return true;
    }
  }
  const uploadButton = (
    <div style={{ padding: '45px 58px' }}>
      {loading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>点击或拖拽上传</div>
    </div>
  );
  const baseUrl = getBaseUrl();
  const action = baseUrl ? baseUrl : '';
  return (
    <>
      <Dragger
        disabled={!isEditable}
        listType="picture-card"
        className={styles['idcard-upload']}
        showUploadList={false}
        action={`${action}/base/oss/common/uploadfile`}
        // action={`/repayment/repay/uploadRepayPic`}
        headers={{ ...getAuthHeaders() }}
        name="file"
        accept=".jpg,.png"
        data={{
          destPath: 'insurance/policy/vehicleLicense/',
          acl: 'PUBLIC_READ',
          attachment: false,
        }} // 后端商量默认格式
        beforeUpload={beforeUpload}
        onChange={handleChange}
      >
        {imageUrl ? <img src={imageUrl} alt="avatar" width="100%" /> : uploadButton}
      </Dragger>
      {imageUrl && (
        <>
          <div className={styles.preview}>
            <Button
              size="small"
              style={{
                color: 'white',
                border: 'none',
                background: 'rgba(0,0,0,0.4)',
              }}
              onClick={(e) => {
                setShowPreview(true);
                e.stopPropagation();
              }}
            >
              预览
            </Button>
          </div>
          <Image
            width={200}
            wrapperStyle={{ display: 'none' }}
            // style={{ display: 'none' }}
            src={imageUrl}
            preview={{
              visible: showPreview,
              // getContainer: () => document.body,
              src: imageUrl,
              onVisibleChange: (visible) => {
                setShowPreview(visible);
              },
            }}
          />
        </>
      )}
    </>
  );
};

export default UploadImage;
