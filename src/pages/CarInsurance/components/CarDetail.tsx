import { useModel } from '@umijs/max';
import { Button, Carousel, Image, Modal, Space, Tooltip } from 'antd';
import dayjs from 'dayjs';
import React, { memo } from 'react';
import styles from '../styles/index.less';
import type { IcarInfoItem, IcarUploadFileItem } from '../type';
import { transformUrlTofileList } from '../utils';
import CarEdit from './CarEdit';
import PreviewPDF from './PreviewPDF';

type Props = {
  carInfoItem: IcarInfoItem;
};
const CarDetail: React.FC<Props> = (props) => {
  const { carInfoItem } = props;
  const { vehicleLicenseUrlList = [], insuranceSlipUrlList = [], uuid, plateNo } = carInfoItem;

  const { setCarInfo, carInfo, isEditable } = useModel('CarInsurance.carInsurance');
  const licenseInfo = [
    {
      label: '车牌号/新车合格证号',
      value: 'plateNo',
    },
    {
      label: '车架号',
      value: 'vin',
    },
    {
      label: '发动机号',
      value: 'engineNumber',
    },
    {
      label: '行驶证车主',
      value: 'vehicleLicenseOwner',
    },
  ];
  const insureInfo = [
    {
      label: '被保险人',
      value: 'assuredName',
    },
    {
      label: '商业险开始日期',
      value: 'commercialInsuranceStartDate',
    },
    {
      label: '商业险结束日期',
      value: 'commercialInsuranceEndDate',
    },
    {
      label: '商业险保单期限(天)',
      value: 'commercialInsurancePeriod',
    },
    {
      label: '商业险保费(元)',
      value: 'commercialInsurancePremium',
    },
    {
      label: '交强险保费(元)',
      value: 'compulsoryInsurancePremium',
    },
    {
      label: '车船税金额(元)',
      value: 'vehicleTaxAmount',
    },
    {
      label: '其他代付金额(元)',
      value: 'otherPaymentAmount',
    },
  ];
  function onDeleteClick() {
    console.log('uuid', uuid);

    Modal.confirm({
      content: `你确认要删除该- ${plateNo} 车辆吗?`,
      onOk() {
        const carInfo1 = carInfo.filter((item) => {
          if (item?.id) {
            // id存在 删除车辆要告诉后端这辆车是否已经删除
            if (!item.deleteFlag) {
              // 已经删过的不需要操作了
              if (item.uuid === uuid) {
                item.deleteFlag = true;
              } else {
                item.deleteFlag = false;
              }
            }
            return true;
          } else {
            // 假如id不存在 则这辆车还没有保存到数据库中 直接删除
            if (item.uuid === uuid) {
              return false;
            } else {
              return true;
            }
          }
        });
        setCarInfo(carInfo1);
      },
    });
  }
  function getValue(field: any) {
    const value = carInfoItem[field];
    if (value || value === 0) {
      return ['commercialInsuranceEndDate', 'commercialInsuranceStartDate'].includes(field)
        ? dayjs(value).format('YYYY-MM-DD')
        : value;
    } else {
      // "" null undefined 表示未选择 用 / 表示
      return '/';
    }
  }

  function renderImgJsx(list: string[]) {
    const fileList = transformUrlTofileList(list);
    if (list?.length === 0) {
      return <span style={{ color: 'red' }}>缺失</span>;
    }
    // "application/pdf" | "image/jpeg" | "image/png" | "image/jpg"
    const imgList: IcarUploadFileItem[] = [];
    const pdfList: IcarUploadFileItem[] = [];

    fileList.forEach((file) => {
      if (file?.type === 'application/pdf') {
        pdfList.push(file);
      } else {
        imgList.push(file);
      }
    });

    return (
      <div>
        <div>
          {
            <Carousel
              dots={{
                className: 'car-carousel',
              }}
            >
              {imgList.map((item) => {
                return (
                  <div key={item.url}>
                    <Image
                      src={item.url}
                      preview={{ maskClassName: 'aaaa' }}
                      className={styles['car-img-preview']}
                      style={{ marginBottom: 20 }}
                      width={240}
                      height={240}
                    />
                  </div>
                );
              })}
            </Carousel>
          }
        </div>
        <div>
          {pdfList.map((item) => {
            const { url, name } = item;
            return (
              <div key={url}>
                <PreviewPDF url={url}>
                  <Tooltip
                    title={
                      <div>
                        <div>左击弹窗预览</div>
                        <div>右击新开标签页预览</div>
                      </div>
                    }
                  >
                    <a
                      onContextMenu={() => {
                        window.open(url);
                      }}
                    >
                      {name}
                    </a>
                  </Tooltip>
                </PreviewPDF>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  function getTotalAmountValue() {
    // 商业险保费+交强险保费+车船税金额+其他代付金额
    const {
      commercialInsurancePremium,
      compulsoryInsurancePremium,
      vehicleTaxAmount,
      otherPaymentAmount,
    } = carInfoItem;
    const amount =
      Number(commercialInsurancePremium) +
      Number(compulsoryInsurancePremium) +
      Number(vehicleTaxAmount) +
      Number(otherPaymentAmount);

    return isNaN(Number(amount)) ? 0 : amount.toFixed(2);
  }

  function renderCarInfoJsx(info: { label: string; value: string }[]) {
    return info.map((item) => {
      const { label, value } = item;
      return (
        <div
          key={value}
          style={{
            display: 'flex',
            gap: 40,
            borderTop: '1px solid #f0f0f0',
            minHeight: 32,
            alignItems: 'center',
          }}
        >
          <div style={{ color: '#8c8c8c', textAlign: 'start' }}>{label}:</div>
          <div style={{ flex: 1 }}>
            <div style={{ whiteSpace: 'nowrap', textAlign: 'end' }}> {getValue(value)}</div>
            <div style={{ whiteSpace: 'nowrap', textAlign: 'end' }}>
              {carInfoItem?.error?.[value] ? (
                <div style={{ color: 'red' }}>{carInfoItem?.error?.[value]}</div>
              ) : null}
            </div>
          </div>
        </div>
      );
    });
  }

  return (
    <div style={{ width: '100%', overflowX: 'auto' }}>
      <div style={{ display: 'flex', justifyContent: 'end' }}>
        <Space>
          <Button type="link" danger onClick={onDeleteClick} disabled={!isEditable}>
            删除此车辆
          </Button>
          <CarEdit carInfoItem={carInfoItem} title="编辑">
            <Button type="link" disabled={!isEditable}>
              编辑
            </Button>
          </CarEdit>
        </Space>
      </div>
      <>
        <div style={{ display: 'flex', justifyContent: 'space-between', gap: 40 }}>
          <div style={{ flex: 1, maxWidth: 240 }}>
            <h3>车辆证件</h3>
            {renderImgJsx(vehicleLicenseUrlList)}
          </div>
          <div style={{ flex: 1, maxWidth: 240 }}>
            <h3>投保单</h3>
            {renderImgJsx(insuranceSlipUrlList)}
          </div>

          <div>
            <h3>相关信息</h3>
            <div>
              {renderCarInfoJsx(licenseInfo)}
              <div style={{ marginBottom: 20 }} />
              {renderCarInfoJsx(insureInfo)}
              <div
                style={{
                  display: 'flex',
                  gap: 40,
                  borderTop: '1px solid #f0f0f0',
                  minHeight: 32,
                  alignItems: 'center',
                }}
              >
                <div style={{ color: '#8c8c8c', textAlign: 'start' }}>保险金额合计(元):</div>
                <div style={{ whiteSpace: 'nowrap', textAlign: 'end', flex: 1 }}>
                  {' '}
                  {getTotalAmountValue()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
      <></>
    </div>
  );
};
export default memo(CarDetail);
