/*
 * @Author: your name
 * @Date: 2020-11-23 16:08:02
 * @LastEditTime: 2025-02-12 14:25:45
 * @LastEditors: oak.yang <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/src/utils/utils.ts
 */

import { PRODUCT_CLASSIFICATION_CODE, SECONDARY_CLASSIFICATION_CODE } from '@/enums';
import { themeToken } from '@/theme';
import { getToken } from '@/utils/auth';
import { useModel } from '@umijs/max';
import BigNumber from 'bignumber.js';
import dayjs from 'dayjs';
import { useRef } from 'react';

const reg = /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;

export const isUrl = (path: string): boolean => reg.test(path);

export const isAntDesignPro = (): boolean => {
  if (ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION === 'site') {
    return true;
  }
  return window.location.hostname === 'preview.pro.ant.design';
};

// 给官方演示站点用，用于关闭真实开发环境不需要使用的特性
export const isAntDesignProOrDev = (): boolean => {
  const { NODE_ENV } = process.env;
  if (NODE_ENV === 'development') {
    return true;
  }
  return isAntDesignPro();
};

export function getVanEvn() {
  let env = 'pre';
  const { vanEnv } = document.documentElement.dataset;
  if (vanEnv) {
    env = vanEnv === 'prod' ? 'prd' : vanEnv;
  }
  return env;
}

/**
 *
 * @param type 标识服务标识
 * @returns {string} 完整的域名地址
 */
export function getBaseUrl(type = '') {
  // risk-fin-stg.huolala.cn
  // finance-api-stg.huolala.cn
  // finance-api-stg.myhll.cn
  // https://lalafin-base-stg.myhll.cn
  // const { host, protocol } = window.location;
  const { vanEnv } = document.documentElement.dataset;
  let baseUrl = '';
  const urlEvn = vanEnv === 'prod' ? '' : `-${vanEnv}`;
  if (vanEnv) {
    if (type === 'base') {
      baseUrl = `https://lalafin-base${urlEvn}.myhll.cn`;
    } else {
      baseUrl = `https://finance-api${urlEvn}.lalafin.net`;
    }
  }
  return baseUrl;
}
export function getTokenUrl(url: string) {
  const token = getToken();
  let urlNew = '';
  if (url.indexOf('?') > 0) {
    if (url.indexOf('token') <= 0) {
      urlNew = `${url}&token=${token}`;
    }
  } else {
    urlNew = `${url}?token=${token}`;
  }
  return urlNew;
}

/**
 * axios 中需要加这两个参数 才能获取到
 *  responseType: 'blob',
    getResponse: true,
 */
export function downLoadExcel(res: any, typeOther?: string) {
  const type = typeOther || 'application/vnd.ms-excel;charset=utf-8'; // excel文件
  // content-disposition: attachment;filename*=utf-8''%E4%BA%A7%E5%93%81%E7%AE%A1%E7%90%86%E5%AF%BC%E5%87%BA.xlsx
  // content-disposition: attachment;filename=2191073504219160576%E7%8E%8B%E6%B4%8B%E5%90%88%E5%90%8C.zip

  // 处理fileName
  const fileName =
    decodeURI(
      res.headers['content-disposition']?.split("filename*=utf-8''")[1] ||
        res.headers['content-disposition']?.split('filename=')[1],
    ) || '附件';

  const blob = new Blob([res.data], { type });
  const link = document.createElement('a');
  const href = window.URL.createObjectURL(blob);
  link.href = href;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link); // 下载完成后移除标签
  window.URL.revokeObjectURL(href);
}

export const getBlob = (url: string, cb: (blob: Blob) => void) => {
  const innerUrl = new URL(url);
  innerUrl.searchParams.append('t', `${new Date().valueOf()}`);
  const xhr = new XMLHttpRequest();
  xhr.open('GET', innerUrl.toString(), true);
  xhr.responseType = 'blob';
  xhr.onload = () => {
    if (xhr.status === 200) {
      cb(xhr.response);
    }
  };
  xhr.send();
};

export const promiseGetBlob = (url: string): Promise<any> => {
  const innerUrl = new URL(url);
  innerUrl.searchParams.append('t', `${new Date().valueOf()}`);
  return new Promise((res, rej) => {
    fetch(innerUrl.toString())
      .then((data) => res(data.blob()))
      .catch((err) => {
        // 可能报了跨域错误，等位置错误，尝试通过浏览器直接访问url
        window.open(url);
        rej(err);
      });
  });
};

export function downloadFileByElementA(href: string, fileName?: string) {
  const link = document.createElement('a');
  link.href = href;
  link.download = fileName || '';
  link.click();
}

export const getBase64 = (file: any) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};

export const saveAs = (blob: Blob, filename: string) => {
  if ((window.navigator as any).msSaveOrOpenBlob) {
    (navigator as any).msSaveBlob(blob, filename);
  } else {
    const link = document.createElement('a');
    const body = document.querySelector('body');
    link.href = window.URL.createObjectURL(blob);
    link.download = filename;
    // fix Firefox
    link.style.display = 'none';
    body?.appendChild(link);
    link.click();
    body?.removeChild(link);
    window.URL.revokeObjectURL(link.href);
  }
};
export const previewAS = (blobParam: Blob, typeExc?: string) => {
  const blob = new Blob([blobParam], {
    type: typeExc || 'application/pdf;chartset=UTF-8',
  });
  const fileURL = URL.createObjectURL(blob);
  window.open(fileURL);
};

// 处理文件名过长
export const genSimpleName = (name: string) => {
  if (name) {
    const pos = name.lastIndexOf('.');
    const fileExtension = name.substring(pos, name.length);
    const fileName = name.substring(0, pos);
    if (fileName.length > 10) {
      const simpleName = fileName.substring(0, 10);
      return `${simpleName}...${fileExtension}`;
    }
    return name;
  }
  return '';
};

/**
 * @Date: 2021-04-26 18:12:53
 * @Author: elisa.zhao
 * @LastEditors: elisa.zhao
 * @LastEditTime: Do not edit
 * @FilePath: Do not edit
 * @mapList:{name1:fileList,name2:fileList2}
 * @sepectionList 额外处理为 [{name:,url:}]格式
 */
export const convertUploadFileList = (
  mapList: Record<string, any> | undefined,
  sepectionList?: string[],
) => {
  const tempMap: any = {};
  // item:name1,name2;mapList[item]=>mapList.name;itemSon:上传file的所有信息{response}
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  mapList &&
    Object.keys(mapList).map((item: string) => {
      // 将map对象根据key转成数组，循环，获取，每个key下的url[]数组
      tempMap[item] = [];
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      mapList[item] &&
        mapList[item].map((itemSon: any) => {
          // console.log(tempMap,itemSon)
          if (itemSon && (itemSon.response || itemSon?.filePath)) {
            if (sepectionList?.includes(item)) {
              tempMap[item].push({
                filePath: itemSon.response?.data?.filePath || itemSon?.filePath || '',
                name: itemSon.name,
              });
            } else {
              tempMap[item].push(itemSon.response?.data?.filePath || itemSon?.filePath || '');
            }
          }
          return null;
        });
      return null;
    });
  return tempMap;
};

/**
 * @Date: 2021-05-10 14:51:22
 * @Author: elisa.zhao
 * @LastEditors: elisa.zhao
 * @LastEditTime: Do not edit
 * @FilePath: Do not edit
 * @param {*} arr 要切割的arr
 * @param {*} size 切成小数组的
 */
export const sliceArray = (arr: any, size: number) => {
  const arr2 = [];
  for (let i = 0; i < arr?.length; i += size) {
    arr2.push(arr?.slice(i, i + size));
  }
  return arr2;
};

export const getUuid = () => {
  const S4 = () => {
    return ((1 + Math.random()) * 0x10000 || 0).toString(16).substring(1);
  };
  return `${S4() + S4()}-${S4()}-${S4()}-${S4()}-${S4()}${S4()}${S4()}`;
};

// 生成逾期查看详情路径
export const createDetailPath = ({
  productCode,
  overdueId,
}: {
  productCode: string;
  overdueId: string;
}) => {
  if (!productCode || !overdueId) return '';
  const resPdCode = productCode.substr(0, 2);
  const BusinessMap: Record<string, any> = {
    [PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING]: {
      label: '明保',
      path: 'overdue-detail',
    },
    [PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE]: {
      label: '融资租赁',
      path: 'overdue-lease-detail',
    },
    [PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN]: {
      label: '小易速贷',
      path: 'overdue-lease-detail',
    },
  };
  const path = `/businessMng/postLoanMng/${BusinessMap[resPdCode].path}?overdueId=${overdueId}&businessType=${BusinessMap[resPdCode].label}&productCode=${productCode}`;
  return path;
};

// 小贷融租订单号-->订单详情
// 保理-->账单详情
export const billNoToOrderOrBillDetail = ({
  productCode,
  billNo,
  accountNumber,
}: {
  productCode: string;
  billNo: string;
  accountNumber: number | string;
}) => {
  if (!productCode || !billNo) return '';
  const resPdCode = productCode.substr(0, 2);
  const second = productCode.substr(0, 4);
  if (second === SECONDARY_CLASSIFICATION_CODE.CAR_INSURANCE) {
    // 如果二级分类是车险分期
    return `/businessMng/car-insurance/detail?orderNo=${billNo}`;
  }

  const BusinessMap: Record<string, any> = {
    [PRODUCT_CLASSIFICATION_CODE.SELLER_FACTORING]: {
      label: '明保',
      path: `/businessMng/bill-detail?billNo=${billNo}&accountNumber=${accountNumber}`,
    },
    [PRODUCT_CLASSIFICATION_CODE.FINANCE_LEASE]: {
      label: '融资租赁',
      path: `/businessMng/lease-detail?orderNo=${billNo}`,
    },
    [PRODUCT_CLASSIFICATION_CODE.SMALL_LOAN]: {
      label: '小易速贷',
      path: `/businessMng/cash-detail?orderNo=${billNo}`,
    },
  };
  const path = `${BusinessMap[resPdCode].path}`;
  return path;
};

export function isImageFile(file: any) {
  if (file.type) {
    return file.type.indexOf('image') === 0;
  }

  return false;
}

const IMAGE_REGEXP = /\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i;

export function isImageUrl(url: string) {
  return IMAGE_REGEXP.test(url);
}

export function isPdfUrl(url: string) {
  const PDF_REGEXP = /\.(pdf)/i;
  return PDF_REGEXP.test(url);
}

/**
 *  读取图片文件
 * @param file
 */
export function asyncReadFileToDataUrl(file: File) {
  return new Promise<string>((resolve, reject) => {
    try {
      console.log('FileReader', FileReader);
      const reader = new FileReader();
      reader.onload = function (evt) {
        const dataUrl = evt?.target?.result as string;
        // console.log('dataUrl onload', dataUrl)
        if (dataUrl) {
          resolve(dataUrl);
        } else {
          reject();
        }
      };
      reader.onerror = function (e: any) {
        console.log('reader onerror', e);
        reject();
      };
      reader.readAsDataURL(file);
    } catch {
      console.log('reader catch');
      reject();
    }
  });
}

/**
 * base64数据转为图片
 * @param dataUrl base64数据串
 */
export async function readDataUrlToImg(dataUrl: string) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.src = dataUrl;
    img.onload = function () {
      resolve(img);
    };
    img.onerror = function (e) {
      reject(e);
    };
  });
}

/**
 * 压缩图片
 */
export async function compressImg(
  img: any,
  type: string,
  mx: number,
  mh: number,
): Promise<Blob | null> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    const { width: originWidth, height: originHeight } = img;
    // 最大尺寸限制
    const maxWidth = mx;
    const maxHeight = mh;
    // 目标尺寸
    let targetWidth = originWidth;
    let targetHeight = originHeight;
    if (originWidth > maxWidth || originHeight > maxHeight) {
      if (originWidth / originHeight > 1) {
        // 宽图片
        targetWidth = maxWidth;
        targetHeight = Math.round(maxWidth * (originHeight / originWidth));
      } else {
        // 高图片
        targetHeight = maxHeight;
        targetWidth = Math.round(maxHeight * (originWidth / originHeight));
      }
    }
    canvas.width = targetWidth;
    canvas.height = targetHeight;
    context?.clearRect(0, 0, targetWidth, targetHeight);
    // 图片绘制
    context?.drawImage(img, 0, 0, targetWidth, targetHeight);
    canvas.toBlob((blob) => {
      resolve(blob);
    }, type || 'image/png');
  });
}

export function fen2yuan(num: number, toString?: boolean) {
  if (!num) return 0;
  const value = new BigNumber(num);
  if (toString) return value.div(100).toString();
  return value.div(100).toNumber();
}

export function bigNumberFen2Yuan(num: number) {
  return new BigNumber(num).div(100).toNumber();
}
/**
 * 是否是外网 当前的域名是否是外网 "https://biz-admin.lalafin.net"
 */
export function isExternalNetwork() {
  // 如果内容 且 当前用户 仅是 委外的角色 也应该返回 true
  // 可以 传一个 roleCode

  // 外网测试
  if (localStorage.getItem('externalNetworkDebugger') === '1') {
    return true;
  }
  // 在stg、pre增加一级域名判断，模拟外网环境
  const env = getVanEvn();
  if (['stg', 'pre'].includes(env) && window.location.hostname?.includes('.lalafin.net')) {
    return true;
  }
  return window.location.origin === 'https://biz-admin.lalafin.net';
}
/**
 * 外网角色信息是否需要脱敏
 */
export function isExternalDesensitization(access: any) {
  // 业务系统_委外催收专员
  return access?.hasRole('biz_externalCallAgent');
}
/**
 * 渠道和门店帐号筛选归类判断
 */
export function isChannelStoreUser(access: any) {
  // 业务系统_融租渠道门店用户/业务系统_融租渠道用户
  // return access?.hasRole('leaseChannelUser') || access?.hasRole('leaseStoreUser');
  return access?.hasAccess('biz_view_finance_lease_external_data');
}
/**
 * 外网与渠道用户判断
 */
export function isChannelOrExternal(access: any) {
  return isChannelStoreUser(access) || isExternalNetwork();
}
/**
 * @Description:  是否是车险用户
 */
export function isCarInsuranceStoreUser(access: any) {
  return access?.hasAccess('biz_view_car_insurance_data');
}

/**
 * 是否切换成为备用账号密码登录通道
 */
export function isStandbyLogin() {
  return false;
  // return (
  //   (window.location.pathname === '/user/login' &&
  //     window.location.search &&
  //     window.location.search.includes('type=standby')) ||
  //   localStorage.getItem('standby') === 'account'
  // );
}
export function setIsStandbyLogin() {
  localStorage.setItem('standby', 'account');
}
export function removeStandbyLoginStorage() {
  localStorage.removeItem('standby');
}

/**
 * 判断是否是一个合法json 字符串
 * true 是json false 不是json 且序列化后的值是否 存在
 */
export function isJsonAndExist(str?: any): boolean {
  try {
    // JSON.parse(null) = null 也可以被
    const res = JSON.parse(str);
    if (res) return true;
    return false;
  } catch (error) {
    return false;
  }
}

/**
 * 将手机号脱敏
 * *********** -- 151****4538
 */

export function desensitizationPhone(phone?: string): string {
  if (!phone) {
    return '';
  }
  const arr = phone.split('');
  arr.splice(3, 4, '****');
  return arr.join('');
}

/**
 * 将银行卡号和身份证号脱敏
 * 仅展示 前四后四
 */

export function desensitizationBankAndIdCard(bankCode: string): string {
  if (!bankCode) {
    return '';
  }
  const arr = bankCode.split('');
  const num = arr.length - 4 - 4;
  const str = '*';
  arr.splice(4, num, str.repeat(num));
  return arr.join('');
  // *********
}

/**
 * 获取 Repayment 服务 上传图片的完整路径
 * 由于antd上传图片默认的action不经过umi-request,所以app.tsx中设置的base url线上不生效
 * @returns 完整的路径
 * 覆盖率: src/pages/BusinessExcel/components/UploadExcel.tsx
 */
export function getRepaymentUploadAction() {
  // http://lalafin-base-stg.myhll.cn
  const path = '/repayment/oss/common/uploadfile';
  const { vanEnv } = document.documentElement.dataset;
  // vanEnv 存在 stg pre prod
  // 不存在 本地走代理 直接返回路径
  let baseUrl = '';
  if (vanEnv) {
    const urlEvn = vanEnv === 'prod' ? '' : `-${vanEnv}`;
    baseUrl = `https://finance-api${urlEvn}.lalafin.net`;
  }
  return baseUrl + path;
}

// cookie相关
export function getCookieMap() {
  const cookieArray = document.cookie.split('; ');
  const cookieMap: any = {};
  for (let i = 0; i < cookieArray.length; i++) {
    const index = cookieArray[i].indexOf('=');
    const itemName = cookieArray[i].slice(0, index);
    const itemValue = cookieArray[i].slice(index + 1);
    cookieMap[itemName] = itemValue;
  }
  return cookieMap;
}
export function getCookieItem(key: string) {
  const cookieMap: any = getCookieMap() || {};
  return cookieMap[key];
}
export function setCookieItem(key: string, value: string) {
  document.cookie = `${key}=${value}`;
}

/**
 * 转换大数金额
 * @param value
 */
export function transformMoney(value: number) {
  if (!value) return '';
  const newValue = ['', '', ''];
  let fr = 1000;
  let num = 3;

  while (value / fr >= 1) {
    fr *= 10;
    num += 1;
    // console.log('数字', value / fr, 'num:', num);
  }
  if (num <= 4) {
    // 千
    newValue[1] = '千';
    newValue[0] = `${parseInt(`${value / 1000}`, 10)}`;
  } else if (num <= 8) {
    // 万
    const text1 = parseInt(`${num - 4}`, 10) / 3 > 1 ? '千万' : '万';
    // tslint:disable-next-line:no-shadowed-variable
    const fm = text1 === '万' ? 10000 : 10000000;
    newValue[1] = text1;
    newValue[0] = `${(value / fm).toFixed(2)}`;
  } else if (num <= 16) {
    // 亿
    let text1 = (num - 8) / 3 > 1 ? '千亿' : '亿';
    text1 = (num - 8) / 4 > 1 ? '万亿' : text1;
    text1 = (num - 8) / 7 > 1 ? '千万亿' : text1;
    // tslint:disable-next-line:no-shadowed-variable
    let fm = 1;
    if (text1 === '亿') {
      fm = 100000000;
    } else if (text1 === '千亿') {
      fm = 100000000000;
    } else if (text1 === '万亿') {
      fm = 1000000000000;
    } else if (text1 === '千万亿') {
      fm = 1000000000000000;
    }
    newValue[1] = text1;
    newValue[0] = `${parseInt(`${value / fm}`, 10)}`;
  }
  if (value < 1000) {
    newValue[1] = '';
    newValue[0] = `${value}`;
  }
  return newValue;
}

export function combindTheme(theme: Record<string, any>, customStyle?: Record<string, any>) {
  if (!customStyle) return theme;

  const result = { ...theme };
  // 递归合并
  for (const key in customStyle) {
    if (Object.prototype.hasOwnProperty.call(customStyle, key)) {
      if (typeof customStyle[key] === 'object' && customStyle[key] !== null) {
        result[key] = combindTheme(result[key] || {}, customStyle[key]);
      } else {
        if (result[key] !== customStyle[key]) {
          result[key] = customStyle[key];
        }
      }
    }
  }
  return result;
}

export function getTheme(customStyle?: Record<string, any>) {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const { initialState } = useModel('@@initialState');
  const { navTheme } = initialState?.settings || {};

  if (navTheme === 'realDark') {
    return combindTheme(themeToken, customStyle?.realDark);
  }
  return combindTheme(themeToken, customStyle?.light);
}

/**
 * react 深度比较, 避免重复渲染，from react-use源码
 * @param a
 * @param b
 * @param ignoreKeys
 * @param debug
 * @returns
 */
export function isDeepEqualReact(a: any, b: any, ignoreKeys?: string[], debug?: boolean) {
  if (a === b) return true;

  if (a && b && typeof a === 'object' && typeof b === 'object') {
    if (a.constructor !== b.constructor) return false;

    let length;
    let i;
    let keys;
    if (Array.isArray(a)) {
      length = a.length;
      if (length != b.length) return false;
      for (i = length; i-- !== 0; )
        if (!isDeepEqualReact(a[i], b[i], ignoreKeys, debug)) return false;
      return true;
    }

    if (a instanceof Map && b instanceof Map) {
      if (a.size !== b.size) return false;
      for (i of a.entries()) if (!b.has(i[0])) return false;
      for (i of a.entries())
        if (!isDeepEqualReact(i[1], b.get(i[0]), ignoreKeys, debug)) return false;
      return true;
    }

    if (a instanceof Set && b instanceof Set) {
      if (a.size !== b.size) return false;
      for (i of a.entries()) if (!b.has(i[0])) return false;
      return true;
    }

    if (ArrayBuffer.isView(a) && ArrayBuffer.isView(b)) {
      // @ts-ignore
      length = a.length;
      // @ts-ignore
      if (length != b.length) return false;
      // @ts-ignore
      for (i = length; i-- !== 0; ) if (a[i] !== b[i]) return false;
      return true;
    }

    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;
    if (a.valueOf !== Object.prototype.valueOf && a.valueOf) return a.valueOf() === b.valueOf();
    if (a.toString !== Object.prototype.toString && a.toString)
      return a.toString() === b.toString();

    // eslint-disable-next-line prefer-const
    keys = Object.keys(a);
    length = keys.length;
    if (length !== Object.keys(b).length) return false;

    for (i = length; i-- !== 0; )
      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;

    for (i = length; i-- !== 0; ) {
      const key = keys[i];

      if (ignoreKeys?.includes(key)) continue;

      if (key === '_owner' && a.$$typeof) {
        // React-specific: avoid traversing React elements' _owner.
        //  _owner contains circular references
        // and is not needed when comparing the actual elements (and not their owners)
        continue;
      }

      if (!isDeepEqualReact(a[key], b[key], ignoreKeys, debug)) {
        if (debug) {
          console.log(key);
        }
        return false;
      }
    }

    return true;
  }

  // true if both NaN, false otherwise
  return a !== a && b !== b;
}

/**
 * 深度比较
 * @param a
 * @param b
 * @param ignoreKeys
 * @returns
 */
export const isDeepEqual = (a: any, b: any, ignoreKeys?: string[]) =>
  isDeepEqualReact(a, b, ignoreKeys);

/**
 * react 深度比较, 避免重复渲染
 * @param value
 * @param ignoreKeys
 * @returns
 */
export function useDeepCompareMemoize(value: any, ignoreKeys?: any) {
  const ref = useRef();
  // it can be done by using useMemo as well
  // but useRef is rather cleaner and easier
  if (!isDeepEqual(value, ref.current, ignoreKeys)) {
    ref.current = value;
  }

  return ref.current;
}
// 禁用今天之后的未来日期
export function disableFutureDate(current: any) {
  return current && current > dayjs().endOf('day');
}

// 获取宿主浏览器信息
export function getDeviceInfo() {
  const deviceInfo = {
    systemType: '',
    browser: '',
    network: '',
  };
  //
  try {
    //
    deviceInfo.systemType = navigator?.platform || '';
    deviceInfo.browser = navigator?.appVersion || '';
    deviceInfo.network = navigator?.connection?.effectiveType || '';
  } catch (e) {
    console.log(e);
  }
  return deviceInfo;
}

export function chunkArray(array, chunkSize) {
  if (!array || !array.length) return [];
  return Array.from({ length: Math.ceil(array.length / chunkSize) }, (_, index) =>
    array.slice(index * chunkSize, (index + 1) * chunkSize),
  );
}
