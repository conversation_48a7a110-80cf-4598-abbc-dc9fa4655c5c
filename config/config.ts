/*
 * @Author: your name
 * @Date: 2020-11-23 16:08:02
 * @LastEditTime: 2024-03-15 18:11:39
 * @LastEditors: elisa.z<PERSON>
 * @Description: In User Settings Edit
 * @FilePath: /lala-finance-biz-web/config/config.ts
 */
// https://umijs.org/config/
import { defineConfig } from '@umijs/max';
import defaultSettings from './defaultSettings';
import proxy from './proxy';
import routes from './routes';

const { REACT_APP_ENV, NODE_ENV } = process.env;
const isProduction = NODE_ENV === 'production';

// console.log('构建环境', process.env.npm_config_hll_env);
// console.log('构建国家', process.env.npm_config_hll_country);

let reactSrcipt = [];
if (process.env.NODE_ENV === 'production') {
  reactSrcipt = [
    'https://static.huolala.cn/libs/react/17.0.2/umd/react.production.min.js',
    'https://static.huolala.cn/libs/react-dom/17.0.2/umd/react-dom.production.min.js',
  ];
} else {
  reactSrcipt = [
    'https://static.huolala.cn/libs/react/17.0.2/umd/react.development.js',
    'https://static.huolala.cn/libs/react-dom/17.0.2/umd/react-dom.development.js',
  ];
}

export default defineConfig({
  define: {
    HLL_APP_ENV: process.env.npm_config_hll_env,
  },
  publicPath: process.env.VAN == 'true' ? '__VAN_STATIC_BASE_PATH__/' : '/',
  model: {},
  access: {},
  request: {},
  initialState: {},
  historyWithQuery: {},
  hash: true,
  antd: {},
  dva: {},
  fastRefresh: true,
  layout: {
    name: 'Ant Design Pro',
    locale: false,
    ...defaultSettings,
  },
  targets: {
    ie: 11,
  },
  jsMinifier: 'terser',
  routes, // 路由配置
  plugins: ['umi-plugin-keep-alive'],
  externals: {
    react: 'window.React',
    'react-dom': 'window.ReactDOM',
    lodash: 'window._',
    'lodash/padStart': 'window._.padStart',
    'lodash/padEnd': 'window._.padEnd',
    'lodash/isEqual': 'window._.isEqual',
    'lodash/debounce': 'window._.debounce',
    'lodash/throttle': 'window._.throttle',
    'lodash/difference': 'window._.difference',
    'lodash/groupBy': 'window._.groupBy',
    'lodash/uniq': 'window._.uniq',
  },

  chainWebpack(memo: any) {
    // if (process.env.NODE_ENV === 'production') {
    //   // 生产环境的 source map 设置
    //   memo.devtool('source-map');
    // }
    return memo;
  },
  headScripts: [
    ...reactSrcipt,
    // 'https://static.huolala.cn/libs/react/16.14.0/umd/react.production.min.js',
    // `https://static.huolala.cn/libs/react/17.0.2/umd/react.${process.env.NODE_ENV}.min.js`,
    // 'https://static.huolala.cn/libs/react-dom/16.14.0/umd/react-dom.production.min.js',
    // `https://static.huolala.cn/libs/react-dom/17.0.2/umd/react-dom.${process.env.NODE_ENV}.min.js`,
    'https://static.huolala.cn/libs/lodash/4.17.21/lodash.min.js',
    'https://static.huolala.cn/npm/xlsx@0.18.5/dist/xlsx.full.min.js',
  ],
  extraBabelPlugins: ['lodash', isProduction ? 'transform-remove-console' : ''],
  // chunks: ['vendors', 'umi'],
  // dynamicImport: {
  //   loading: '@/components/PageLoading/index',
  // },

  // van: {
  //   eslint: false,
  // },
  ignoreMomentLocale: true,
  proxy: proxy[REACT_APP_ENV || 'dev'],
  manifest: {
    basePath: '/',
  },
  // 开发环境使用eval，生产环境不使用source-map
  devtool: process.env.NODE_ENV === 'development' ? 'eval' : false,
  // locale: false,
  // mako: {},
});
